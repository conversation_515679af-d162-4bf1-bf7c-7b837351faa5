<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم الخلطات الإسفلتية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <!-- Add XLSX library for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&family=Almarai:wght@300;400;700&display=swap');
        
        :root {
            --primary-color: #5D5CDE;
            --primary-dark: #4646B3;
            --primary-light: #7F7CFF;
            --secondary-color: #4CAF50;
            --secondary-dark: #388E3C;
            --secondary-light: #81C784;
            --accent-color: #FF9800;
            --accent-dark: #F57C00;
            --accent-light: #FFB74D;
            --background-light: #FFFFFF;
            --text-dark: #333333;
            --text-muted: #6c757d;
            --error-color: #F44336;
            --border-light: #E0E0E0;
            --card-light: #F8F8F8;
            --table-header-light: #F0F0F0;
            --table-row-odd-light: #FFFFFF;
            --table-row-even-light: #F5F5F5;
        }
        
        body {
            font-family: 'Cairo', 'Almarai', sans-serif;
            background-color: var(--background-light);
            color: var(--text-dark);
        }
        
        .rtl {
            direction: rtl;
        }
        
        .ltr {
            direction: ltr;
        }
        
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        
        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
        
        /* Custom transition effects */
        .transition-all {
            transition: all 0.3s ease;
        }
        
        /* Custom input styles */
        .input-field {
            border: 1px solid var(--border-light);
            border-radius: 0.375rem;
            padding: 0.5rem 1rem;
            background-color: var(--background-light);
            color: var(--text-dark);
        }
        
        /* Input with units */
        .input-with-unit {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .input-unit {
            font-size: 0.85rem;
            color: var(--text-muted);
            min-width: 20px;
            text-align: right;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background-color: var(--secondary-dark);
        }
        
        .tab-active {
            border-bottom: 3px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: 600;
        }
        
        /* Table styles */
        .table-header {
            background-color: var(--table-header-light);
        }
        
        .table-row-odd {
            background-color: var(--table-row-odd-light);
        }
        
        .table-row-even {
            background-color: var(--table-row-even-light);
        }
        
        /* Social icons */
        .social-icon {
            transition: transform 0.3s ease;
        }
        
        .social-icon:hover {
            transform: scale(1.2);
        }
        
        /* Card styles */
        .card {
            background-color: var(--card-light);
            border: 1px solid var(--border-light);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        /* Badge */
        .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            display: inline-block;
            font-weight: 600;
        }
        
        .badge-primary {
            background-color: var(--primary-light);
            color: white;
        }
        
        .badge-success {
            background-color: var(--secondary-light);
            color: white;
        }
        
        .badge-warning {
            background-color: var(--accent-light);
            color: white;
        }
        
        /* Custom tooltip styles */
        .custom-tooltip {
            position: relative;
            cursor: help;
        }
        
        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 14px;
        }
        
        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        
        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        /* Print styles for PDF */
        @media print {
            body {
                background-color: white !important;
                color: black !important;
            }
            
            .print-white {
                background-color: white !important;
                color: black !important;
            }
            
            .print-border {
                border: 1px solid #000 !important;
            }
            
            .print-text {
                font-size: 12px !important;
                line-height: 1.3 !important;
            }
            
            .print-small-text {
                font-size: 10px !important;
                line-height: 1.2 !important;
            }
            
            .print-title {
                font-size: 16px !important;
                font-weight: bold !important;
                margin-bottom: 8px !important;
            }
            
            .print-subtitle {
                font-size: 14px !important;
                font-weight: bold !important;
                margin: 10px 0 6px 0 !important;
            }
            
            .print-table-compact th,
            .print-table-compact td {
                padding: 4px !important;
                font-size: 10px !important;
            }
            
            .print-page-break {
                page-break-before: always;
            }
            
            .print-no-break {
                page-break-inside: avoid;
            }
        }
        
        /* Added styles for thickness calculation explanation */
        .explanation-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .explanation-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 8px;
        }
        
        .formula {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin: 8px 0;
            direction: ltr;
            text-align: center;
            font-family: monospace;
        }
        
        /* Info notification style */
        .info-box {
            background-color: #fffdf1;
            border-right: 4px solid #ffc107;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-white text-gray-800">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-md py-3">
            <div class="container mx-auto px-4 flex justify-between items-center">
                <div class="flex items-center">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOCAxMkw0IDRMMjAgMTJMMTYgMjBMOCAxMloiIGZpbGw9IiM1RDVDREUiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==" alt="Logo" class="h-8 w-8 ml-2">
                    <h1 class="text-2xl font-bold text-primary-600" id="app-title">تصميم الخلطات الإسفلتية</h1>
                </div>
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Version Badge -->
                    <span class="badge badge-primary">الإصدار 2.2</span>
                    
                    <!-- Language Toggle -->
                    <button id="language-toggle" class="px-3 py-1 border border-gray-300 rounded-lg hover:bg-gray-50">
                        <span id="language-text">English</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow container mx-auto px-4 py-6">
            <!-- Tabs -->
            <div class="mb-6 border-b border-gray-300">
                <div class="flex flex-wrap -mb-px">
                    <button id="tab-project" class="tab-btn tab-active py-2 px-4 text-base font-medium">
                        <span class="lang-ar">معلومات المشروع</span>
                        <span class="lang-en hidden">Project Information</span>
                    </button>
                    <button id="tab-gradation" class="tab-btn py-2 px-4 text-base font-medium">
                        <span class="lang-ar">التدرج المنخلي</span>
                        <span class="lang-en hidden">Sieve Gradation</span>
                    </button>
                    <button id="tab-thickness" class="tab-btn py-2 px-4 text-base font-medium">
                        <span class="lang-ar">سماكة الطبقات</span>
                        <span class="lang-en hidden">Pavement Thickness</span>
                    </button>
                    <button id="tab-analysis" class="tab-btn py-2 px-4 text-base font-medium">
                        <span class="lang-ar">تحليل الخلطة</span>
                        <span class="lang-en hidden">Mix Analysis</span>
                    </button>
                    <button id="tab-report" class="tab-btn py-2 px-4 text-base font-medium">
                        <span class="lang-ar">التقرير</span>
                        <span class="lang-en hidden">Report</span>
                    </button>
                    <button id="tab-saved" class="tab-btn py-2 px-4 text-base font-medium">
                        <span class="lang-ar">المشاريع المحفوظة</span>
                        <span class="lang-en hidden">Saved Projects</span>
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div id="tab-contents">
                <!-- Project Information Tab -->
                <div id="content-project" class="tab-content">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">معلومات المشروع</span>
                                <span class="lang-en hidden">Project Information</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">أدخل معلومات المشروع الأساسية، بما في ذلك بيانات المرور والخصائص المادية. تؤثر هذه البيانات على تصميم سماكة الطبقات والخلطة الإسفلتية.</span>
                                        <span class="lang-en hidden">Enter basic project information, including traffic data and material properties. This data affects the design of layer thickness and asphalt mix.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">اسم المشروع</span>
                                    <span class="lang-en hidden">Project Name</span>
                                    <span class="tooltip-text lang-ar">اسم المشروع الذي سيظهر في التقارير والمستندات</span>
                                    <span class="tooltip-text lang-en hidden">The project name that will appear in reports and documents</span>
                                </label>
                                <input type="text" id="project-name" class="input-field w-full text-base" placeholder="مثال: طريق المطار الدولي" />
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">الموقع</span>
                                    <span class="lang-en hidden">Location</span>
                                    <span class="tooltip-text lang-ar">موقع المشروع الجغرافي والإحداثيات إن وجدت</span>
                                    <span class="tooltip-text lang-en hidden">The geographic location of the project and coordinates if available</span>
                                </label>
                                <input type="text" id="project-location" class="input-field w-full text-base" placeholder="مثال: بغداد - المنطقة الشمالية" />
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">نوع التربة</span>
                                    <span class="lang-en hidden">Soil Type</span>
                                    <span class="tooltip-text lang-ar">نوع التربة يؤثر على تصميم طبقات الأساس والرصف</span>
                                    <span class="tooltip-text lang-en hidden">Soil type affects the design of base and pavement layers</span>
                                </label>
                                <select id="soil-type" class="input-field w-full text-base">
                                    <option value="sandy" class="lang-ar">رملية</option>
                                    <option value="sandy" class="lang-en hidden">Sandy</option>
                                    <option value="clayey" class="lang-ar">طينية</option>
                                    <option value="clayey" class="lang-en hidden">Clayey</option>
                                    <option value="silty" class="lang-ar">سلتية</option>
                                    <option value="silty" class="lang-en hidden">Silty</option>
                                    <option value="gravelly" class="lang-ar">حصوية</option>
                                    <option value="gravelly" class="lang-en hidden">Gravelly</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">قيمة CBR (%)</span>
                                    <span class="lang-en hidden">CBR Value (%)</span>
                                    <span class="tooltip-text lang-ar">نسبة تحمل كاليفورنيا - مؤشر لقوة التربة، تتراوح عادة بين 2-80%</span>
                                    <span class="tooltip-text lang-en hidden">California Bearing Ratio - an indicator of soil strength, typically ranges between 2-80%</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="cbr-value" class="input-field w-full text-base" min="0" max="100" value="6" placeholder="قيمة CBR" />
                                    <span class="input-unit">%</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">درجة حرارة الخدمة</span>
                                    <span class="lang-en hidden">Service Temperature</span>
                                    <span class="tooltip-text lang-ar">درجة الحرارة المتوسطة التي ستتعرض لها الطبقات الإسفلتية أثناء فترة الخدمة</span>
                                    <span class="tooltip-text lang-en hidden">The average temperature that the asphalt layers will be exposed to during the service period</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="service-temp" class="input-field w-full text-base" value="40" placeholder="درجة الحرارة" />
                                    <span class="input-unit">°C</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">مستوى حركة المرور</span>
                                    <span class="lang-en hidden">Traffic Level</span>
                                    <span class="tooltip-text lang-ar">المستوى العام لكثافة حركة المرور وأهمية الطريق</span>
                                    <span class="tooltip-text lang-en hidden">The general level of traffic density and the importance of the road</span>
                                </label>
                                <select id="traffic-level" class="input-field w-full text-base">
                                    <option value="low" class="lang-ar">منخفض</option>
                                    <option value="low" class="lang-en hidden">Low</option>
                                    <option value="medium" class="lang-ar">متوسط</option>
                                    <option value="medium" class="lang-en hidden">Medium</option>
                                    <option value="high" class="lang-ar" selected>عالي</option>
                                    <option value="high" class="lang-en hidden">High</option>
                                    <option value="very-high" class="lang-ar">عالي</option>
                                    <option value="very-high" class="lang-en hidden">Very High</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">عدد محاور مكافئة</span>
                                    <span class="lang-en hidden">Equivalent Axle Load</span>
                                    <span class="tooltip-text lang-ar">عدد محاور مكافئة بمليون (عدد المركبات مضروبة بعامل التكافؤ). قيمة تتراوح بين 0.1 و 100 مليون للطرق العادية</span>
                                    <span class="tooltip-text lang-en hidden">Number of equivalent axles in millions (number of vehicles multiplied by the equivalence factor). Value ranges between 0.1 and 100 million for normal roads</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="esal-value" class="input-field w-full text-base" min="0.1" max="100" step="0.1" value="2.5" placeholder="ESAL" />
                                    <span class="input-unit">× 10⁶</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">نسبة زيادة حركة المرور</span>
                                    <span class="lang-en hidden">Traffic Growth Rate</span>
                                    <span class="tooltip-text lang-ar">النسبة المئوية المتوقعة لزيادة حركة المرور سنو، عادة بين 2-6%</span>
                                    <span class="tooltip-text lang-en hidden">The expected percentage increase in traffic annually, usually between 2-6%</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="traffic-growth" class="input-field w-full text-base" min="0" max="20" step="0.5" value="3" placeholder="نسبة الزيادة" />
                                    <span class="input-unit">%</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">العمر الافتراضي للطريق</span>
                                    <span class="lang-en hidden">Design Life</span>
                                    <span class="tooltip-text lang-ar">عدد السنوات المتوقعة لخدمة الطريق قبل الحاجة لإعادة التأهيل الكاملة</span>
                                    <span class="tooltip-text lang-en hidden">The expected number of years for the road to serve before the need for complete rehabilitation</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="design-life" class="input-field w-full text-base" min="1" max="50" value="20" placeholder="عدد السنوات" />
                                    <span class="input-unit">سنة</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">معامل الموثوقية</span>
                                    <span class="lang-en hidden">Reliability Factor</span>
                                    <span class="tooltip-text lang-ar">درجة الثقة في تصميم الرصف، تتراوح من 50% للطرق الفرعية إلى 99% للطرق السريعة والحيوية</span>
                                    <span class="tooltip-text lang-en hidden">The degree of confidence in the pavement design, ranging from 50% for secondary roads to 99% for highways and vital roads</span>
                                </label>
                                <div class="input-with-unit">
                                    <input type="number" id="reliability-factor" class="input-field w-full text-base" min="50" max="99.9" step="0.1" value="90" placeholder="معامل الموثوقية" />
                                    <span class="input-unit">%</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block mb-2 text-sm font-medium custom-tooltip">
                                    <span class="lang-ar">نوع المواصفة</span>
                                    <span class="lang-en hidden">Specification Type</span>
                                    <span class="tooltip-text lang-ar">نوع المواصفة المستخدمة في تصميم الخلطة الإسفلتية وتقييم نتائج الاختبارات</span>
                                    <span class="tooltip-text lang-en hidden">The type of specification used in designing the asphalt mix and evaluating test results</span>
                                </label>
                                <select id="spec-type" class="input-field w-full text-base">
                                    <option value="astm" class="lang-both">ASTM</option>
                                    <option value="aashto" class="lang-both" selected>AASHTO</option>
                                    <option value="iraqi" class="lang-ar">المواصفة العراقية</option>
                                    <option value="iraqi" class="lang-en hidden">Iraqi Specification</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-8">
                            <div class="flex items-center mb-4">
                                <div class="bg-primary-100 rounded-full p-2 ml-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold">
                                    <span class="lang-ar">معامل المرونة للطبقات</span>
                                    <span class="lang-en hidden">Elastic Modulus for Layers</span>
                                </h3>
                            </div>
                            
                            <div class="info-box mb-6">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="mr-3">
                                        <p class="text-sm text-gray-800">
                                            <span class="lang-ar">معامل المرونة يمثل صلابة المادة وقدرتها على مقاومة التشوه تحت الأحمال. القيم الأعلى تشير إلى مواد أكثر صلابة.</span>
                                            <span class="lang-en hidden">The elastic modulus represents the material's stiffness and ability to resist deformation under loads. Higher values indicate stiffer materials.</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block mb-2 text-sm font-medium custom-tooltip">
                                        <span class="lang-ar">طبقة السطح</span>
                                        <span class="lang-en hidden">Surface Layer</span>
                                        <span class="tooltip-text lang-ar">معامل المرونة لطبقة السطح الإسفلتية، يتراوح عادة بين 2000-4000 ميجاباسكال</span>
                                        <span class="tooltip-text lang-en hidden">Elastic modulus for the asphalt surface layer, typically ranges between 2000-4000 MPa</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="surface-modulus" class="input-field w-full text-base" min="1000" max="5000" value="3000" placeholder="معامل المرونة" />
                                        <span class="input-unit">MPa</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block mb-2 text-sm font-medium custom-tooltip">
                                        <span class="lang-ar">طبقة الرابطة</span>
                                        <span class="lang-en hidden">Binder Layer</span>
                                        <span class="tooltip-text lang-ar">معامل المرونة لطبقة الرابطة، يتراوح عادة بين 2000-3500 ميجاباسكال</span>
                                        <span class="tooltip-text lang-en hidden">Elastic modulus for the binder layer, typically ranges between 2000-3500 MPa</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="binder-modulus" class="input-field w-full text-base" min="1000" max="5000" value="2500" placeholder="معامل المرونة" />
                                        <span class="input-unit">MPa</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block mb-2 text-sm font-medium custom-tooltip">
                                        <span class="lang-ar">طبقة الأساس</span>
                                        <span class="lang-en hidden">Base Layer</span>
                                        <span class="tooltip-text lang-ar">معامل المرونة لطبقة الأساس، يتراوح عادة بين 1500-2500 ميجاباسكال</span>
                                        <span class="tooltip-text lang-en hidden">Elastic modulus for the base layer, typically ranges between 1500-2500 MPa</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="base-modulus" class="input-field w-full text-base" min="1000" max="3000" value="2000" placeholder="معامل المرونة" />
                                        <span class="input-unit">MPa</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-8 flex justify-between">
                            <button id="save-project" class="bg-green-600 hover:bg-green-700 text-white py-2 px-6 rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                </svg>
                                <span class="lang-ar">حفظ المشروع</span>
                                <span class="lang-en hidden">Save Project</span>
                            </button>
                            <button id="next-to-gradation" class="btn-primary py-2 px-6 rounded-lg flex items-center">
                                <span class="lang-ar">التالي</span>
                                <span class="lang-en hidden">Next</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sieve Gradation Tab -->
                <div id="content-gradation" class="tab-content hidden">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">التدرج المنخلي</span>
                                <span class="lang-en hidden">Sieve Gradation</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">يجب أن يكون التدرج المنخلي ضمن الحدود المسموح بها حسب المواصفات. التدرج المناسب يضمن خصائص ميكانيكية جيدة للخلطة الإسفلتية.</span>
                                        <span class="lang-en hidden">Sieve gradation must be within the allowable limits according to specifications. Proper gradation ensures good mechanical properties of the asphalt mix.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                <span class="lang-ar">نوع الخلطة</span>
                                <span class="lang-en hidden">Mix Type</span>
                                <span class="tooltip-text lang-ar">نوع الخلطة يحدد حدود المواصفات للتدرج المنخلي وخصائص الخلطة</span>
                                <span class="tooltip-text lang-en hidden">The mix type determines specification limits for sieve gradation and mix properties</span>
                            </label>
                            <div class="flex space-x-4 rtl:space-x-reverse">
                                <label class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white">
                                    <input type="radio" name="mix-type" value="surface" class="form-radio text-primary-600 h-4 w-4" checked>
                                    <span class="mr-2 text-gray-700">
                                        <span class="lang-ar">سطحية</span>
                                        <span class="lang-en hidden">Surface</span>
                                    </span>
                                </label>
                                <label class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white">
                                    <input type="radio" name="mix-type" value="binder" class="form-radio text-primary-600 h-4 w-4">
                                    <span class="mr-2 text-gray-700">
                                        <span class="lang-ar">رابطة</span>
                                        <span class="lang-en hidden">Binder</span>
                                    </span>
                                </label>
                                <label class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white">
                                    <input type="radio" name="mix-type" value="base" class="form-radio text-primary-600 h-4 w-4">
                                    <span class="mr-2 text-gray-700">
                                        <span class="lang-ar">أساس</span>
                                        <span class="lang-en hidden">Base</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto border border-gray-200 rounded-lg mb-6">
                            <table class="w-full text-sm text-right">
                                <thead class="table-header">
                                    <tr>
                                        <th class="px-4 py-3 font-medium border-b">
                                            <span class="lang-ar">المنخل</span>
                                            <span class="lang-en hidden">Sieve</span>
                                        </th>
                                        <th class="px-4 py-3 font-medium border-b">
                                            <span class="lang-ar">قيمة المار (%)</span>
                                            <span class="lang-en hidden">Passing (%)</span>
                                        </th>
                                        <th class="px-4 py-3 font-medium border-b">
                                            <span class="lang-ar">الحد الأدنى (%)</span>
                                            <span class="lang-en hidden">Min (%)</span>
                                        </th>
                                        <th class="px-4 py-3 font-medium border-b">
                                            <span class="lang-ar">الحد الأعلى (%)</span>
                                            <span class="lang-en hidden">Max (%)</span>
                                        </th>
                                        <th class="px-4 py-3 font-medium border-b">
                                            <span class="lang-ar">المطابقة</span>
                                            <span class="lang-en hidden">Conformity</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="sieve-table-body">
                                    <!-- Will be filled by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Quick fill buttons -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            <button id="fill-min-values" class="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 text-sm">
                                <span class="lang-ar">ملء بالحدود الدنيا</span>
                                <span class="lang-en hidden">Fill with Min Values</span>
                            </button>
                            <button id="fill-max-values" class="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 text-sm">
                                <span class="lang-ar">ملء بالحدود العليا</span>
                                <span class="lang-en hidden">Fill with Max Values</span>
                            </button>
                            <button id="fill-mid-values" class="px-3 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 text-sm">
                                <span class="lang-ar">ملء بالقيم المتوسطة</span>
                                <span class="lang-en hidden">Fill with Middle Values</span>
                            </button>
                            <button id="clear-values" class="px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200 text-sm">
                                <span class="lang-ar">مسح القيم</span>
                                <span class="lang-en hidden">Clear Values</span>
                            </button>
                        </div>
                        
                        <div class="mt-6 flex justify-between">
                            <button id="back-to-project" class="border border-gray-300 py-2 px-6 rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                <span class="lang-ar">السابق</span>
                                <span class="lang-en hidden">Previous</span>
                            </button>
                            <button id="next-to-thickness" class="btn-primary py-2 px-6 rounded-lg flex items-center">
                                <span class="lang-ar">التالي</span>
                                <span class="lang-en hidden">Next</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pavement Thickness Tab -->
                <div id="content-thickness" class="tab-content hidden">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">حساب سماكة طبقات الرصف</span>
                                <span class="lang-en hidden">Pavement Thickness Calculation</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">يتم حساب سماكة طبقات الرصف باستخدام طريقة AASHTO 1993، بناءً على عدد محاور مكافئة، ومعاملات الطبقات، ومعامل الموثوقية.</span>
                                        <span class="lang-en hidden">Pavement thickness is calculated using the AASHTO 1993 method, based on ESAL, layer coefficients, and reliability factor.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Added thickness calculation explanation -->
                        <div class="explanation-box mb-6">
                            <div class="explanation-title">
                                <span class="lang-ar">كيفية حساب نسب سماكة الطبقات</span>
                                <span class="lang-en hidden">How Layer Thickness Ratios Are Calculated</span>
                            </div>
                            <div class="text-sm">
                                <p class="mb-2 lang-ar">
                                    1. يتم أولاً حساب رقم البنية الإنشائية (SN) باستخدام معادلة AASHTO 1993 بناءً على أحمال المرور وموثوقية التصميم وخصائص المواد.
                                </p>
                                <p class="mb-2 lang-en hidden">
                                    1. First, the Structural Number (SN) is calculated using the AASHTO 1993 equation based on traffic loads, design reliability, and material properties.
                                </p>
                                <p class="mb-2 lang-ar">
                                    2. تُحدد النسب الموصى بها لكل طبقة حسب مستوى حركة المرور. على سبيل المثال، للمرور العالي:
                                    <br>• طبقة السطح: 15-25% من السماكة الكلية
                                    <br>• طبقة الرابطة: 35-45% من السماكة الكلية
                                    <br>• طبقة الأساس: 35-50% من السماكة الكلية
                                </p>
                                <p class="mb-2 lang-en hidden">
                                    2. Recommended proportions for each layer are determined based on traffic level. For example, for high traffic:
                                    <br>• Surface layer: 15-25% of total thickness
                                    <br>• Binder layer: 35-45% of total thickness
                                    <br>• Base layer: 35-50% of total thickness
                                </p>
                                <p class="mb-2 lang-ar">
                                    3. تُحسب السماكة الكلية للرصف (بالسنتيمتر) من رقم البنية الإنشائية:
                                </p>
                                <p class="mb-2 lang-en hidden">
                                    3. The total pavement thickness (in cm) is calculated from the Structural Number:
                                </p>
                                <div class="formula">
                                    <span class="lang-ar">السماكة الكلية (سم) = SN / [(a₁×p₁) + (a₂×m₂×p₂) + (a₃×m₃×p₃)] × 2.54</span>
                                    <span class="lang-en hidden">Total Thickness (cm) = SN / [(a₁×p₁) + (a₂×m₂×p₂) + (a₃×m₃×p₃)] × 2.54</span>
                                </div>
                                <p class="mb-1 lang-ar">حيث:</p>
                                <p class="mb-1 lang-en hidden">Where:</p>
                                <ul class="list-disc text-sm list-inside mb-2">
                                    <li class="lang-ar">a₁, a₂, a₃: معاملات الطبقات</li>
                                    <li class="lang-en hidden">a₁, a₂, a₃: Layer coefficients</li>
                                    <li class="lang-ar">m₂, m₃: معاملات التصريف</li>
                                    <li class="lang-en hidden">m₂, m₃: Drainage coefficients</li>
                                    <li class="lang-ar">p₁, p₂, p₃: النسب المئوية للطبقات (بالعشرية)</li>
                                    <li class="lang-en hidden">p₁, p₂, p₃: Layer percentages (in decimal)</li>
                                </ul>
                                <p class="mb-2 lang-ar">
                                    4. تُحسب سماكة كل طبقة من خلال ضرب السماكة الكلية بنسبة الطبقة، ثم تُقرّب إلى أقرب 0.5 سم:
                                </p>
                                <p class="mb-2 lang-en hidden">
                                    4. Each layer's thickness is calculated by multiplying the total thickness by the layer ratio, then rounded to the nearest 0.5 cm:
                                </p>
                                <div class="formula">
                                    <span class="lang-ar">سماكة الطبقة = نسبة الطبقة × السماكة الكلية</span>
                                    <span class="lang-en hidden">Layer Thickness = Layer Ratio × Total Thickness</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <div class="mb-6">
                                    <div class="flex items-center mb-4">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-md font-semibold">
                                            <span class="lang-ar">معاملات الطبقات</span>
                                            <span class="lang-en hidden">Layer Coefficients</span>
                                        </h3>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 gap-4">
                                        <div>
                                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                                <span class="lang-ar">معامل طبقة السطح (a₁)</span>
                                                <span class="lang-en hidden">Surface Layer Coefficient (a₁)</span>
                                                <span class="tooltip-text lang-ar">معامل طبقة السطح، يتراوح بين 0.40-0.44 وفق لمواصفات AASHTO</span>
                                                <span class="tooltip-text lang-en hidden">Surface layer coefficient, ranges between 0.40-0.44 according to AASHTO specifications</span>
                                            </label>
                                            <div class="input-with-unit">
                                                <input type="number" id="surface-coef" class="input-field w-full text-base" min="0" max="1" step="0.01" value="0.42" />
                                                <span class="input-unit"></span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                                <span class="lang-ar">معامل طبقة الرابطة (a₂)</span>
                                                <span class="lang-en hidden">Binder Layer Coefficient (a₂)</span>
                                                <span class="tooltip-text lang-ar">معامل طبقة الرابطة، يتراوح بين 0.35-0.40 وفق لمواصفات AASHTO</span>
                                                <span class="tooltip-text lang-en hidden">Binder layer coefficient, ranges between 0.35-0.40 according to AASHTO specifications</span>
                                            </label>
                                            <div class="input-with-unit">
                                                <input type="number" id="binder-coef" class="input-field w-full text-base" min="0" max="1" step="0.01" value="0.38" />
                                                <span class="input-unit"></span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                                <span class="lang-ar">معامل طبقة الأساس (a₃)</span>
                                                <span class="lang-en hidden">Base Layer Coefficient (a₃)</span>
                                                <span class="tooltip-text lang-ar">معامل طبقة الأساس، يتراوح بين 0.12-0.18 وفق لمواصفات AASHTO</span>
                                                <span class="tooltip-text lang-en hidden">Base layer coefficient, ranges between 0.12-0.18 according to AASHTO specifications</span>
                                            </label>
                                            <div class="input-with-unit">
                                                <input type="number" id="base-coef" class="input-field w-full text-base" min="0" max="1" step="0.01" value="0.14" />
                                                <span class="input-unit"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-6">
                                    <div class="flex items-center mb-4">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                            </svg>
                                        </div>
                                        <h3 class="text-md font-semibold">
                                            <span class="lang-ar">معاملات التصريف</span>
                                            <span class="lang-en hidden">Drainage Coefficients</span>
                                        </h3>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 gap-4">
                                        <div>
                                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                                <span class="lang-ar">معامل تصريف طبقة الرابطة (m₂)</span>
                                                <span class="lang-en hidden">Binder Drainage Coefficient (m₂)</span>
                                                <span class="tooltip-text lang-ar">معامل تصريف المياه لطبقة الرابطة، يتراوح من 0.8 (سيء) إلى 1.2 (ممتاز)</span>
                                                <span class="tooltip-text lang-en hidden">Water drainage coefficient for the binder layer, ranges from 0.8 (poor) to 1.2 (excellent)</span>
                                            </label>
                                            <div class="input-with-unit">
                                                <input type="number" id="binder-drainage" class="input-field w-full text-base" min="0.5" max="1.4" step="0.1" value="1.0" />
                                                <span class="input-unit"></span>
                                            </div>
                                        </div>
                                        
                                        <div>
                                            <label class="block mb-2 text-sm font-medium custom-tooltip">
                                                <span class="lang-ar">معامل تصريف طبقة الأساس (m₃)</span>
                                                <span class="lang-en hidden">Base Drainage Coefficient (m₃)</span>
                                                <span class="tooltip-text lang-ar">معامل تصريف المياه لطبقة الأساس، يتراوح من 0.8 (سيء) إلى 1.2 (ممتاز)</span>
                                                <span class="tooltip-text lang-en hidden">Water drainage coefficient for the base layer, ranges from 0.8 (poor) to 1.2 (excellent)</span>
                                            </label>
                                            <div class="input-with-unit">
                                                <input type="number" id="base-drainage" class="input-field w-full text-base" min="0.5" max="1.4" step="0.1" value="1.0" />
                                                <span class="input-unit"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-6">
                                    <button id="calculate-thickness" class="btn-primary py-3 px-6 rounded-lg flex items-center justify-center w-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                        <span class="lang-ar">حساب السماكة</span>
                                        <span class="lang-en hidden">Calculate Thickness</span>
                                    </button>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex items-center mb-4">
                                    <div class="bg-primary-100 rounded-full p-1 ml-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <h3 class="text-md font-semibold">
                                        <span class="lang-ar">نتائج حساب السماكة</span>
                                        <span class="lang-en hidden">Thickness Results</span>
                                    </h3>
                                </div>
                                
                                <div class="bg-gray-50 p-5 rounded-lg mb-6 shadow-sm">
                                    <table class="w-full">
                                        <thead class="border-b-2 border-gray-200">
                                            <tr>
                                                <th class="text-right px-2 py-2 text-sm font-semibold">
                                                    <span class="lang-ar">الطبقة</span>
                                                    <span class="lang-en hidden">Layer</span>
                                                </th>
                                                <th class="text-right px-2 py-2 text-sm font-semibold">
                                                    <span class="lang-ar">السماكة (سم)</span>
                                                    <span class="lang-en hidden">Thickness (cm)</span>
                                                </th>
                                                <th class="text-right px-2 py-2 text-sm font-semibold">
                                                    <span class="lang-ar">النسبة (%)</span>
                                                    <span class="lang-en hidden">Percentage (%)</span>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="border-b">
                                                <td class="px-2 py-3">
                                                    <span class="lang-ar">طبقة السطح</span>
                                                    <span class="lang-en hidden">Surface Layer</span>
                                                </td>
                                                <td class="px-2 py-3 font-medium" id="surface-thickness">--</td>
                                                <td class="px-2 py-3" id="surface-percentage">--</td>
                                            </tr>
                                            <tr class="border-b">
                                                <td class="px-2 py-3">
                                                    <span class="lang-ar">طبقة الرابطة</span>
                                                    <span class="lang-en hidden">Binder Layer</span>
                                                </td>
                                                <td class="px-2 py-3 font-medium" id="binder-thickness">--</td>
                                                <td class="px-2 py-3" id="binder-percentage">--</td>
                                            </tr>
                                            <tr class="border-b">
                                                <td class="px-2 py-3">
                                                    <span class="lang-ar">طبقة الأساس</span>
                                                    <span class="lang-en hidden">Base Layer</span>
                                                </td>
                                                <td class="px-2 py-3 font-medium" id="base-thickness">--</td>
                                                <td class="px-2 py-3" id="base-percentage">--</td>
                                            </tr>
                                            <tr>
                                                <td class="px-2 py-3 font-semibold">
                                                    <span class="lang-ar">إجمالي السماكة</span>
                                                    <span class="lang-en hidden">Total Thickness</span>
                                                </td>
                                                <td class="px-2 py-3 font-semibold" id="total-thickness">--</td>
                                                <td class="px-2 py-3 font-semibold">100%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="flex justify-between items-center mb-2">
                                    <div class="flex items-center">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <h3 class="text-md font-semibold">
                                            <span class="lang-ar">رقم البنية الإنشائية (SN)</span>
                                            <span class="lang-en hidden">Structural Number (SN)</span>
                                        </h3>
                                    </div>
                                    <div class="bg-primary-600 text-white px-3 py-1 rounded-lg text-xl font-bold" id="structural-number">--</div>
                                </div>
                                
                                <div id="thickness-recommendations" class="info-box mb-4 hidden">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="mr-3">
                                            <p class="text-sm text-gray-800" id="thickness-recommendation-text">
                                                <!-- Will be filled by JavaScript -->
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card rounded-lg p-2 border border-gray-200">
                                    <canvas id="thickness-chart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-between">
                            <button id="back-to-gradation" class="border border-gray-300 py-2 px-6 rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                <span class="lang-ar">السابق</span>
                                <span class="lang-en hidden">Previous</span>
                            </button>
                            <button id="next-to-analysis" class="btn-primary py-2 px-6 rounded-lg flex items-center">
                                <span class="lang-ar">التالي</span>
                                <span class="lang-en hidden">Next</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mix Analysis Tab -->
                <div id="content-analysis" class="tab-content hidden">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">تحليل الخلطة</span>
                                <span class="lang-en hidden">Mix Analysis</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">تحليل الخلطة يحدد خصائصها الفيزيائية والميكانيكية، بناءً على نسب المكونات والتدرج المنخلي وظروف الخدمة.</span>
                                        <span class="lang-en hidden">Mix analysis determines its physical and mechanical properties, based on component ratios, sieve gradation, and service conditions.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <div class="bg-gray-50 p-5 rounded-lg mb-6 shadow-sm">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                        <h3 class="font-medium">
                                            <span class="lang-ar">نتائج التحليل</span>
                                            <span class="lang-en hidden">Analysis Results</span>
                                        </h3>
                                    </div>
                                    
                                    <div id="analysis-results" class="space-y-3">
                                        <div class="flex justify-between border-b pb-2">
                                            <span class="lang-ar font-medium">النسبة المثلى للزفت:</span>
                                            <span class="lang-en hidden font-medium">Optimum Asphalt Content:</span>
                                            <span id="optimum-asphalt" class="font-semibold">--</span>
                                        </div>
                                        <div class="flex justify-between border-b pb-2">
                                            <span class="lang-ar font-medium">الفراغات الهوائية:</span>
                                            <span class="lang-en hidden font-medium">Air Voids:</span>
                                            <span id="air-voids" class="font-semibold">--</span>
                                        </div>
                                        <div class="flex justify-between border-b pb-2">
                                            <span class="lang-ar font-medium">الكثافة النظرية القصوى:</span>
                                            <span class="lang-en hidden font-medium">Theoretical Maximum Density:</span>
                                            <span id="max-density" class="font-semibold">--</span>
                                        </div>
                                        <div class="flex justify-between border-b pb-2">
                                            <span class="lang-ar font-medium">نسبة مطابقة التدرج:</span>
                                            <span class="lang-en hidden font-medium">Gradation Conformity:</span>
                                            <span id="gradation-conformity" class="font-semibold">--</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-6">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </div>
                                        <h3 class="font-medium">
                                            <span class="lang-ar">الخصائص المتوقعة</span>
                                            <span class="lang-en hidden">Expected Properties</span>
                                        </h3>
                                    </div>
                                    
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
                                            <span class="block text-sm text-gray-500 mb-1">
                                                <span class="lang-ar">ثبات مارشال</span>
                                                <span class="lang-en hidden">Marshall Stability</span>
                                            </span>
                                            <span id="marshall-stability" class="text-lg font-medium">--</span>
                                        </div>
                                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
                                            <span class="block text-sm text-gray-500 mb-1">
                                                <span class="lang-ar">الانسياب</span>
                                                <span class="lang-en hidden">Flow</span>
                                            </span>
                                            <span id="flow-value" class="text-lg font-medium">--</span>
                                        </div>
                                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
                                            <span class="block text-sm text-gray-500 mb-1">
                                                <span class="lang-ar">مقاومة الرطوبة</span>
                                                <span class="lang-en hidden">Moisture Resistance</span>
                                            </span>
                                            <span id="moisture-resistance" class="text-lg font-medium">--</span>
                                        </div>
                                        <div class="bg-gray-50 p-4 rounded-lg shadow-sm">
                                            <span class="block text-sm text-gray-500 mb-1">
                                                <span class="lang-ar">مقاومة التخدد</span>
                                                <span class="lang-en hidden">Rutting Resistance</span>
                                            </span>
                                            <span id="rutting-resistance" class="text-lg font-medium">--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="card rounded-lg p-3 mb-4 shadow-sm">
                                    <canvas id="gradation-chart" height="250"></canvas>
                                </div>
                                
                                <div class="bg-gray-50 p-5 rounded-lg shadow-sm">
                                    <div class="flex items-center mb-3">
                                        <div class="bg-primary-100 rounded-full p-1 ml-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </div>
                                        <h3 class="font-medium">
                                            <span class="lang-ar">توصيات التحسين</span>
                                            <span class="lang-en hidden">Improvement Recommendations</span>
                                        </h3>
                                    </div>
                                    <div id="recommendations" class="text-sm mt-2">
                                        <!-- Will be filled by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-between">
                            <button id="back-to-thickness" class="border border-gray-300 py-2 px-6 rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                <span class="lang-ar">السابق</span>
                                <span class="lang-en hidden">Previous</span>
                            </button>
                            <button id="next-to-report" class="btn-primary py-2 px-6 rounded-lg flex items-center">
                                <span class="lang-ar">إنشاء التقرير</span>
                                <span class="lang-en hidden">Generate Report</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Tab -->
                <div id="content-report" class="tab-content hidden">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">تقرير التصميم</span>
                                <span class="lang-en hidden">Design Report</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">يمكنك تنزيل التقرير بصيغة PDF أو Excel. يحتوي التقرير على جميع البيانات والنتائج والتوصيات.</span>
                                        <span class="lang-en hidden">You can download the report in PDF or Excel format. The report contains all data, results, and recommendations.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div id="report-content" class="p-6 border border-gray-200 rounded-lg min-h-[400px] mb-6 bg-white text-gray-800 overflow-auto">
                            <!-- Will be filled by JavaScript -->
                        </div>
                        
                        <div class="flex justify-between">
                            <button id="back-to-analysis" class="border border-gray-300 py-2 px-6 rounded-lg flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                </svg>
                                <span class="lang-ar">السابق</span>
                                <span class="lang-en hidden">Previous</span>
                            </button>
                            <div class="space-x-4 rtl:space-x-reverse">
                                <button id="download-pdf" class="bg-red-600 hover:bg-red-700 text-white py-2 px-6 rounded-lg flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                                    </svg>
                                    <span class="lang-ar">تنزيل PDF</span>
                                    <span class="lang-en hidden">Download PDF</span>
                                </button>
                                <button id="download-excel" class="bg-green-600 hover:bg-green-700 text-white py-2 px-6 rounded-lg flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                                    </svg>
                                    <span class="lang-ar">تنزيل Excel</span>
                                    <span class="lang-en hidden">Download Excel</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Saved Projects Tab -->
                <div id="content-saved" class="tab-content hidden">
                    <div class="card rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-6">
                            <div class="bg-primary-100 rounded-full p-2 ml-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">
                                <span class="lang-ar">المشاريع المحفوظة</span>
                                <span class="lang-en hidden">Saved Projects</span>
                            </h2>
                        </div>
                        
                        <!-- Informational note -->
                        <div class="info-box mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm text-gray-800">
                                        <span class="lang-ar">يتم حفظ المشاريع محليً في متصفحك. انقر على مشروع لتحميله، أو على زر الحذف لإزالته.</span>
                                        <span class="lang-en hidden">Projects are saved locally in your browser. Click on a project to load it, or on the delete button to remove it.</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div id="saved-projects-list" class="mb-6">
                            <!-- Will be filled by JavaScript -->
                            <div class="flex flex-col items-center justify-center py-12 text-gray-500" id="no-saved-projects">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                                <span class="lang-ar text-lg">لا توجد مشاريع محفوظة</span>
                                <span class="lang-en hidden text-lg">No saved projects</span>
                                <p class="mt-2 text-sm text-gray-400 max-w-md text-center">
                                    <span class="lang-ar">قم بحفظ المشروع الحالي من خلال زر "حفظ المشروع" في صفحة معلومات المشروع.</span>
                                    <span class="lang-en hidden">Save the current project using the "Save Project" button on the project information page.</span>
                                </p>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button id="clear-saved-projects" class="bg-red-600 hover:bg-red-700 text-white py-2 px-6 rounded-lg hidden items-center mx-auto">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <span class="lang-ar">حذف جميع المشاريع</span>
                                <span class="lang-en hidden">Clear All Projects</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white shadow-inner py-6 mt-6">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-right mb-4 md:mb-0">
                        <p class="text-gray-600 text-sm">
                            <span class="lang-ar">تطبيق حساب سماكة طبقات الرصف وتحليل وتصميم الخلطات الاسفلتية © 2025-2026<br>صُمم بحب لجميع المهندسين من قبل المهندس محمد يونس الجوالي</span>
                            <span class="lang-en hidden">Pavement Thickness Calculation and Asphalt Mix Design App © 2025-2026<br>Designed with love for all engineers by Engineer Mohammed Younis Al-Jawali</span>
                        </p>
                    </div>
                    
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <!-- WhatsApp -->
                        <a href="https://wa.me/9647507812241" target="_blank" class="social-icon text-green-600 hover:text-green-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
                            </svg>
                        </a>
                        
                        <!-- Facebook -->
                        <a href="https://facebook.com/aljwalei" target="_blank" class="social-icon text-blue-600 hover:text-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                            </svg>
                        </a>
                        
                        <!-- Instagram -->
                        <a href="https://instagram.com/m2n.9" target="_blank" class="social-icon text-pink-600 hover:text-pink-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Variables
        let currentLanguage = 'ar';
        let gradationChart = null;
        let thicknessChart = null;
        let savedProjects = [];
        
        // Sieve data
        const sieves = [
            { id: 'sieve-1', size: '1"', sizeAr: '1 بوصة', sizeMm: '25.0' },
            { id: 'sieve-2', size: '3/4"', sizeAr: '3/4 بوصة', sizeMm: '19.0' },
            { id: 'sieve-3', size: '1/2"', sizeAr: '1/2 بوصة', sizeMm: '12.5' },
            { id: 'sieve-4', size: '3/8"', sizeAr: '3/8 بوصة', sizeMm: '9.5' },
            { id: 'sieve-5', size: 'No.4', sizeAr: 'رقم 4', sizeMm: '4.75' },
            { id: 'sieve-6', size: 'No.8', sizeAr: 'رقم 8', sizeMm: '2.36' },
            { id: 'sieve-7', size: 'No.30', sizeAr: 'رقم 30', sizeMm: '0.6' },
            { id: 'sieve-8', size: 'No.50', sizeAr: 'رقم 50', sizeMm: '0.3' },
            { id: 'sieve-9', size: 'No.100', sizeAr: 'رقم 100', sizeMm: '0.15' },
            { id: 'sieve-10', size: 'No.200', sizeAr: 'رقم 200', sizeMm: '0.075' }
        ];
        
        // Specification limits by mix type
        const specLimits = {
            'surface': {
                'astm': [
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 68, max: 88 },
                    { min: 60, max: 80 },
                    { min: 45, max: 65 },
                    { min: 32, max: 52 },
                    { min: 16, max: 34 },
                    { min: 10, max: 26 },
                    { min: 6, max: 18 },
                    { min: 3, max: 8 }
                ],
                'aashto': [
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 70, max: 90 },
                    { min: 56, max: 80 },
                    { min: 35, max: 65 },
                    { min: 23, max: 49 },
                    { min: 12, max: 30 },
                    { min: 7, max: 23 },
                    { min: 5, max: 15 },
                    { min: 2, max: 8 }
                ],
                'iraqi': [
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 75, max: 95 },
                    { min: 60, max: 85 },
                    { min: 40, max: 70 },
                    { min: 28, max: 58 },
                    { min: 15, max: 35 },
                    { min: 8, max: 25 },
                    { min: 5, max: 18 },
                    { min: 3, max: 10 }
                ]
            },
            'binder': {
                'astm': [
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 75, max: 90 },
                    { min: 65, max: 85 },
                    { min: 50, max: 70 },
                    { min: 35, max: 55 },
                    { min: 18, max: 36 },
                    { min: 12, max: 28 },
                    { min: 6, max: 20 },
                    { min: 3, max: 8 }
                ],
                'aashto': [
                    { min: 100, max: 100 },
                    { min: 95, max: 100 },
                    { min: 80, max: 100 },
                    { min: 70, max: 90 },
                    { min: 50, max: 75 },
                    { min: 35, max: 60 },
                    { min: 18, max: 40 },
                    { min: 10, max: 30 },
                    { min: 5, max: 20 },
                    { min: 2, max: 10 }
                ],
                'iraqi': [
                    { min: 100, max: 100 },
                    { min: 95, max: 100 },
                    { min: 83, max: 100 },
                    { min: 75, max: 95 },
                    { min: 55, max: 80 },
                    { min: 40, max: 65 },
                    { min: 20, max: 45 },
                    { min: 12, max: 32 },
                    { min: 6, max: 22 },
                    { min: 3, max: 12 }
                ]
            },
            'base': {
                'astm': [
                    { min: 100, max: 100 },
                    { min: 95, max: 100 },
                    { min: 80, max: 95 },
                    { min: 70, max: 90 },
                    { min: 55, max: 75 },
                    { min: 40, max: 60 },
                    { min: 22, max: 42 },
                    { min: 15, max: 32 },
                    { min: 8, max: 22 },
                    { min: 3, max: 9 }
                ],
                'aashto': [
                    { min: 100, max: 100 },
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 80, max: 95 },
                    { min: 60, max: 85 },
                    { min: 45, max: 70 },
                    { min: 25, max: 45 },
                    { min: 15, max: 35 },
                    { min: 8, max: 25 },
                    { min: 3, max: 12 }
                ],
                'iraqi': [
                    { min: 100, max: 100 },
                    { min: 100, max: 100 },
                    { min: 90, max: 100 },
                    { min: 83, max: 98 },
                    { min: 65, max: 87 },
                    { min: 48, max: 72 },
                    { min: 25, max: 48 },
                    { min: 18, max: 38 },
                    { min: 10, max: 28 },
                    { min: 4, max: 14 }
                ]
            }
        };

        // Recommended thickness ratios
        const recommendedThicknessRatios = {
            'low': {
                'surface': { min: 15, max: 20 },
                'binder': { min: 25, max: 35 },
                'base': { min: 45, max: 60 }
            },
            'medium': {
                'surface': { min: 15, max: 20 },
                'binder': { min: 30, max: 40 },
                'base': { min: 40, max: 55 }
            },
            'high': {
                'surface': { min: 15, max: 25 },
                'binder': { min: 35, max: 45 },
                'base': { min: 35, max: 50 }
            },
            'very-high': {
                'surface': { min: 20, max: 25 },
                'binder': { min: 40, max: 50 },
                'base': { min: 30, max: 40 }
            }
        };

        // Document elements
        const languageToggle = document.getElementById('language-toggle');
        const languageText = document.getElementById('language-text');
        const appTitle = document.getElementById('app-title');
        
        // Tab elements
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // Next/Back buttons
        const nextToGradationBtn = document.getElementById('next-to-gradation');
        const backToProjectBtn = document.getElementById('back-to-project');
        const nextToThicknessBtn = document.getElementById('next-to-thickness');
        const backToGradationBtn = document.getElementById('back-to-gradation');
        const nextToAnalysisBtn = document.getElementById('next-to-analysis');
        const backToThicknessBtn = document.getElementById('back-to-thickness');
        const nextToReportBtn = document.getElementById('next-to-report');
        const backToAnalysisBtn = document.getElementById('back-to-analysis');
        
        // Calculate thickness button
        const calculateThicknessBtn = document.getElementById('calculate-thickness');
        
        // Quick fill buttons
        const fillMinValuesBtn = document.getElementById('fill-min-values');
        const fillMaxValuesBtn = document.getElementById('fill-max-values');
        const fillMidValuesBtn = document.getElementById('fill-mid-values');
        const clearValuesBtn = document.getElementById('clear-values');
        
        // Save project button
        const saveProjectBtn = document.getElementById('save-project');
        const clearSavedProjectsBtn = document.getElementById('clear-saved-projects');
        
        // Custom tooltip handler (replaces tippy.js)
        function setupCustomTooltips() {
            // We already have CSS-only tooltips, no JavaScript needed
        }
        
        // Initialize sieve table
        function initializeSieveTable() {
            const tableBody = document.getElementById('sieve-table-body');
            if (!tableBody) return;
            
            tableBody.innerHTML = '';
            
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            sieves.forEach((sieve, index) => {
                const row = document.createElement('tr');
                row.className = index % 2 === 0 ? 'table-row-odd' : 'table-row-even';
                
                const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                const sieveMm = sieve.sizeMm + ' mm';
                
                row.innerHTML = `
                    <td class="px-4 py-3 border-b">${sieveName} <span class="text-xs text-gray-500">(${sieveMm})</span></td>
                    <td class="px-4 py-3 border-b">
                        <div class="input-with-unit">
                            <input type="number" id="${sieve.id}" class="input-field w-full" min="0" max="100" step="0.1" placeholder="${limits[index].min}-${limits[index].max}">
                            <span class="input-unit">%</span>
                        </div>
                    </td>
                    <td class="px-4 py-3 border-b text-center">${limits[index].min}%</td>
                    <td class="px-4 py-3 border-b text-center">${limits[index].max}%</td>
                    <td class="px-4 py-3 border-b text-center" id="${sieve.id}-conformity">-</td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // Add event listeners to input fields
            sieves.forEach(sieve => {
                const input = document.getElementById(sieve.id);
                if (input) {
                    input.addEventListener('input', checkConformity);
                }
            });
        }
        
        // Fill sieve values with min, max, or mid values
        function fillSieveValues(type) {
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            sieves.forEach((sieve, index) => {
                const input = document.getElementById(sieve.id);
                if (!input) return;
                
                if (type === 'min') {
                    input.value = limits[index].min;
                } else if (type === 'max') {
                    input.value = limits[index].max;
                } else if (type === 'mid') {
                    input.value = ((limits[index].min + limits[index].max) / 2).toFixed(1);
                } else if (type === 'clear') {
                    input.value = '';
                }
            });
            
            // Check conformity
            checkConformity();
        }
        
        // Check conformity of sieve values
        function checkConformity() {
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            let conformCount = 0;
            let totalCount = 0;
            
            sieves.forEach((sieve, index) => {
                const input = document.getElementById(sieve.id);
                const conformityCell = document.getElementById(`${sieve.id}-conformity`);
                if (!input || !conformityCell) return;
                
                if (input.value) {
                    const value = parseFloat(input.value);
                    totalCount++;
                    
                    if (value >= limits[index].min && value <= limits[index].max) {
                        conformityCell.innerHTML = `<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">✓</span>`;
                        conformCount++;
                    } else {
                        conformityCell.innerHTML = `<span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">✗</span>`;
                    }
                } else {
                    conformityCell.textContent = '-';
                }
            });
            
            // Update conformity percentage if there are values
            const gradationConformity = document.getElementById('gradation-conformity');
            if (totalCount > 0 && gradationConformity) {
                const conformityPercentage = (conformCount / totalCount) * 100;
                gradationConformity.textContent = `${conformityPercentage.toFixed(1)}%`;
            }
        }
        
        // Calculate pavement thickness
        function calculatePavementThickness() {
            // Get input values
            const cbrValueEl = document.getElementById('cbr-value');
            if (!cbrValueEl) return null;
            
            const cbrValue = parseFloat(cbrValueEl.value) || 6;
            
            const esalValueEl = document.getElementById('esal-value');
            if (!esalValueEl) return null;
            
            const esalValue = parseFloat(esalValueEl.value) || 2.5; // In millions
            
            const designLifeEl = document.getElementById('design-life');
            if (!designLifeEl) return null;
            
            const designLife = parseFloat(designLifeEl.value) || 20;
            
            const reliabilityFactorEl = document.getElementById('reliability-factor');
            if (!reliabilityFactorEl) return null;
            
            const reliabilityFactor = parseFloat(reliabilityFactorEl.value) || 90;
            
            const trafficLevelEl = document.getElementById('traffic-level');
            if (!trafficLevelEl) return null;
            
            const trafficLevel = trafficLevelEl.value;
            
            // Get layer coefficients
            const surfaceCoefEl = document.getElementById('surface-coef');
            if (!surfaceCoefEl) return null;
            
            const surfaceCoef = parseFloat(surfaceCoefEl.value) || 0.42;
            
            const binderCoefEl = document.getElementById('binder-coef');
            if (!binderCoefEl) return null;
            
            const binderCoef = parseFloat(binderCoefEl.value) || 0.38;
            
            const baseCoefEl = document.getElementById('base-coef');
            if (!baseCoefEl) return null;
            
            const baseCoef = parseFloat(baseCoefEl.value) || 0.14;
            
            // Get drainage coefficients
            const binderDrainageEl = document.getElementById('binder-drainage');
            if (!binderDrainageEl) return null;
            
            const binderDrainage = parseFloat(binderDrainageEl.value) || 1.0;
            
            const baseDrainageEl = document.getElementById('base-drainage');
            if (!baseDrainageEl) return null;
            
            const baseDrainage = parseFloat(baseDrainageEl.value) || 1.0;
            
            // Calculate subgrade resilient modulus (MR) based on CBR
            const resilientModulus = 1500 * cbrValue;
            
            // Calculate reliability factor (ZR)
            let zR;
            if (reliabilityFactor >= 99.9) zR = -3.750;
            else if (reliabilityFactor >= 99.0) zR = -2.327;
            else if (reliabilityFactor >= 95.0) zR = -1.645;
            else if (reliabilityFactor >= 90.0) zR = -1.282;
            else if (reliabilityFactor >= 85.0) zR = -1.037;
            else if (reliabilityFactor >= 80.0) zR = -0.841;
            else if (reliabilityFactor >= 75.0) zR = -0.674;
            else if (reliabilityFactor >= 70.0) zR = -0.524;
            else if (reliabilityFactor >= 60.0) zR = -0.253;
            else zR = 0;
            
            // Standard deviation (S0)
            const standardDeviation = 0.45;
            
            // Terminal serviceability (pt)
            const terminalServiceability = 2.5;
            
            // Initial serviceability (p0)
            const initialServiceability = 4.2;
            
            // Calculate change in serviceability (ΔPSI)
            const deltaServiceability = initialServiceability - terminalServiceability;
            
            // Calculate W18 (design ESALs in millions)
            const trafficGrowthEl = document.getElementById('traffic-growth');
            if (!trafficGrowthEl) return null;
            
            const trafficGrowth = parseFloat(trafficGrowthEl.value) / 100 || 0.03;
            const w18 = esalValue * 1000000 * (Math.pow((1 + trafficGrowth), designLife) - 1) / trafficGrowth;
            
            // Calculate required structural number (SN)
            // Using the AASHTO 1993 Design Equation
            // log10(W18) = ZR*S0 + 9.36*log10(SN+1) - 0.20 + log10[ΔPSI/(4.2-1.5)] / (0.40+1094/(SN+1)^5.19) + 2.32*log10(MR) - 8.07
            
            // Solve for SN using an iterative approach
            let sn = 1.0;
            let step = 0.5;
            let iterations = 0;
            const maxIterations = 100;
            
            while (iterations < maxIterations) {
                iterations++;
                
                // Calculate right side of equation
                const rightSide = zR * standardDeviation + 
                    9.36 * Math.log10(sn + 1) - 0.20 + 
                    Math.log10(deltaServiceability / (4.2 - 1.5)) / 
                    (0.40 + 1094 / Math.pow(sn + 1, 5.19)) + 
                    2.32 * Math.log10(resilientModulus) - 8.07;
                
                // Calculate left side of equation
                const leftSide = Math.log10(w18);
                
                if (Math.abs(leftSide - rightSide) < 0.01) {
                    break;
                }
                
                if (leftSide > rightSide) {
                    sn += step;
                } else {
                    sn -= step;
                    step /= 2;
                    sn += step;
                }
            }
            
            // Round SN to 2 decimal places
            sn = Math.round(sn * 100) / 100;
            
            // Get recommended thickness ratios based on traffic level
            const recommendedRatios = recommendedThicknessRatios[trafficLevel];
            
            // Calculate layer thicknesses based on recommended ratios and total SN
            // Use mid-point of recommended ratios for initial calculation
            const surfacePercentage = (recommendedRatios.surface.min + recommendedRatios.surface.max) / 2;
            const binderPercentage = (recommendedRatios.binder.min + recommendedRatios.binder.max) / 2;
            const basePercentage = 100 - surfacePercentage - binderPercentage;
            
            // Convert to decimal form
            const p1 = surfacePercentage / 100;
            const p2 = binderPercentage / 100;
            const p3 = basePercentage / 100;
            
            // Calculate total thickness in inches
            // SN = a₁×D₁ + a₂×m₂×D₂ + a₃×m₃×D₃
            // Where D₁ = p₁×TotalThickness, D₂ = p₂×TotalThickness, D₃ = p₃×TotalThickness
            // So: SN = TotalThickness × (a₁×p₁ + a₂×m₂×p₂ + a₃×m₃×p₃)
            const totalThicknessInches = sn / (surfaceCoef * p1 + binderCoef * binderDrainage * p2 + baseCoef * baseDrainage * p3);
            
            // Convert to cm (1 inch = 2.54 cm)
            const totalThicknessCm = totalThicknessInches * 2.54;
            
            // Calculate individual layer thicknesses (in cm)
            const surfaceThickness = Math.ceil(totalThicknessCm * p1 * 2) / 2; // Round to nearest 0.5 cm
            const binderThickness = Math.ceil(totalThicknessCm * p2 * 2) / 2;
            const baseThickness = Math.ceil(totalThicknessCm * p3 * 2) / 2;
            
            // Calculate total thickness after rounding
            const calculatedTotalThickness = surfaceThickness + binderThickness + baseThickness;
            
            // Recalculate actual percentages
            const actualSurfacePercentage = (surfaceThickness / calculatedTotalThickness * 100).toFixed(1);
            const actualBinderPercentage = (binderThickness / calculatedTotalThickness * 100).toFixed(1);
            const actualBasePercentage = (baseThickness / calculatedTotalThickness * 100).toFixed(1);
            
            // Update thickness results
            const surfaceThicknessEl = document.getElementById('surface-thickness');
            if (surfaceThicknessEl) surfaceThicknessEl.textContent = `${surfaceThickness.toFixed(1)} cm`;
            
            const binderThicknessEl = document.getElementById('binder-thickness');
            if (binderThicknessEl) binderThicknessEl.textContent = `${binderThickness.toFixed(1)} cm`;
            
            const baseThicknessEl = document.getElementById('base-thickness');
            if (baseThicknessEl) baseThicknessEl.textContent = `${baseThickness.toFixed(1)} cm`;
            
            const totalThicknessEl = document.getElementById('total-thickness');
            if (totalThicknessEl) totalThicknessEl.textContent = `${calculatedTotalThickness.toFixed(1)} cm`;
            
            const structuralNumberEl = document.getElementById('structural-number');
            if (structuralNumberEl) structuralNumberEl.textContent = `${sn.toFixed(2)}`;
            
            const surfacePercentageEl = document.getElementById('surface-percentage');
            if (surfacePercentageEl) surfacePercentageEl.textContent = `${actualSurfacePercentage}%`;
            
            const binderPercentageEl = document.getElementById('binder-percentage');
            if (binderPercentageEl) binderPercentageEl.textContent = `${actualBinderPercentage}%`;
            
            const basePercentageEl = document.getElementById('base-percentage');
            if (basePercentageEl) basePercentageEl.textContent = `${actualBasePercentage}%`;
            
            // Show recommendations
            const thicknessRecommendations = document.getElementById('thickness-recommendations');
            const recommendationText = document.getElementById('thickness-recommendation-text');
            
            if (!thicknessRecommendations || !recommendationText) return null;
            
            // Check if thicknesses are within recommended ranges
            const surfaceInRange = parseFloat(actualSurfacePercentage) >= recommendedRatios.surface.min && 
                                 parseFloat(actualSurfacePercentage) <= recommendedRatios.surface.max;
            
            const binderInRange = parseFloat(actualBinderPercentage) >= recommendedRatios.binder.min && 
                                parseFloat(actualBinderPercentage) <= recommendedRatios.binder.max;
            
            const baseInRange = parseFloat(actualBasePercentage) >= recommendedRatios.base.min && 
                              parseFloat(actualBasePercentage) <= recommendedRatios.base.max;
            
            thicknessRecommendations.classList.remove('hidden');
            
            if (currentLanguage === 'ar') {
                if (surfaceInRange && binderInRange && baseInRange) {
                    recommendationText.innerHTML = `
                        نسب سماكات الطبقات متوافقة مع المعايير الموصى بها وفقًا لمستوى حركة المرور. 
                        الهيكل الإنشائي للرصف مناسب للظروف المرورية والبيئية للمشروع.
                    `;
                } else {
                    let recText = 'بعض نسب سماكات الطبقات خارج النطاق الموصى به:';
                    
                    if (!surfaceInRange) {
                        recText += ` <br>• طبقة السطح ${actualSurfacePercentage}% (الموصى به: ${recommendedRatios.surface.min}-${recommendedRatios.surface.max}%)`;
                    }
                    
                    if (!binderInRange) {
                        recText += ` <br>• طبقة الرابطة ${actualBinderPercentage}% (الموصى به: ${recommendedRatios.binder.min}-${recommendedRatios.binder.max}%)`;
                    }
                    
                    if (!baseInRange) {
                        recText += ` <br>• طبقة الأساس ${actualBasePercentage}% (الموصى به: ${recommendedRatios.base.min}-${recommendedRatios.base.max}%)`;
                    }
                    
                    recText += '<br><br>يمكن تعديل معاملات الطبقات للوصول إلى النسب الموصى بها.';
                    
                    recommendationText.innerHTML = recText;
                }
            } else {
                if (surfaceInRange && binderInRange && baseInRange) {
                    recommendationText.innerHTML = `
                        Layer thickness ratios are in compliance with the recommended standards according to traffic level. 
                        The pavement structure is suitable for the project's traffic and environmental conditions.
                    `;
                } else {
                    let recText = 'Some layer thickness ratios are outside the recommended range:';
                    
                    if (!surfaceInRange) {
                        recText += ` <br>• Surface layer ${actualSurfacePercentage}% (Recommended: ${recommendedRatios.surface.min}-${recommendedRatios.surface.max}%)`;
                    }
                    
                    if (!binderInRange) {
                        recText += ` <br>• Binder layer ${actualBinderPercentage}% (Recommended: ${recommendedRatios.binder.min}-${recommendedRatios.binder.max}%)`;
                    }
                    
                    if (!baseInRange) {
                        recText += ` <br>• Base layer ${actualBasePercentage}% (Recommended: ${recommendedRatios.base.min}-${recommendedRatios.base.max}%)`;
                    }
                    
                    recText += '<br><br>Layer coefficients can be adjusted to achieve the recommended ratios.';
                    
                    recommendationText.innerHTML = recText;
                }
            }
            
            // Update thickness chart
            updateThicknessChart(surfaceThickness, binderThickness, baseThickness);
            
            return {
                surfaceThickness,
                binderThickness,
                baseThickness,
                totalThickness: calculatedTotalThickness,
                structuralNumber: sn,
                surfacePercentage: actualSurfacePercentage,
                binderPercentage: actualBinderPercentage,
                basePercentage: actualBasePercentage
            };
        }
        
        // Update thickness chart (showing relationship between thickness and service years)
        function updateThicknessChart(surfaceThickness, binderThickness, baseThickness) {
            const thicknessChartEl = document.getElementById('thickness-chart');
            if (!thicknessChartEl) return;
            
            const ctx = thicknessChartEl.getContext('2d');
            if (!ctx) return;
            
            // Calculate thicknesses for different service years
            const serviceYears = [5, 10, 15, 20, 25, 30];
            const thicknessValues = serviceYears.map(years => {
                const designLifeEl = document.getElementById('design-life');
                if (!designLifeEl) return { surface: 0, binder: 0, base: 0, total: 0 };
                
                const originalDesignLife = parseFloat(designLifeEl.value) || 20;
                const scaleFactor = Math.pow(years / originalDesignLife, 0.25); // Use a power function to model relationship
                
                return {
                    surface: surfaceThickness * scaleFactor,
                    binder: binderThickness * scaleFactor,
                    base: baseThickness * scaleFactor,
                    total: (surfaceThickness + binderThickness + baseThickness) * scaleFactor
                };
            });
            
            // If chart exists, destroy it
            if (thicknessChart) {
                thicknessChart.destroy();
            }
            
            // Create new chart
            thicknessChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: serviceYears.map(year => `${year} ${currentLanguage === 'ar' ? 'سنة' : 'years'}`),
                    datasets: [
                        {
                            label: currentLanguage === 'ar' ? 'طبقة السطح' : 'Surface Layer',
                            data: thicknessValues.map(t => t.surface),
                            backgroundColor: '#FF6384',
                            borderColor: '#FF6384',
                            borderWidth: 1
                        },
                        {
                            label: currentLanguage === 'ar' ? 'طبقة الرابطة' : 'Binder Layer',
                            data: thicknessValues.map(t => t.binder),
                            backgroundColor: '#36A2EB',
                            borderColor: '#36A2EB',
                            borderWidth: 1
                        },
                        {
                            label: currentLanguage === 'ar' ? 'طبقة الأساس' : 'Base Layer',
                            data: thicknessValues.map(t => t.base),
                            backgroundColor: '#4BC0C0',
                            borderColor: '#4BC0C0',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: currentLanguage === 'ar' ? 'سماكة الطبقات مقابل سنوات الخدمة' : 'Layer Thickness vs Service Years',
                            font: {
                                size: 16
                            },
                        },
                        legend: {
                            position: 'bottom',
                        },
                        tooltip: {
                            callbacks: {
                                afterBody: function(tooltipItems) {
                                    const idx = tooltipItems[0].dataIndex;
                                    return `${currentLanguage === 'ar' ? 'إجمالي السماكة' : 'Total Thickness'}: ${thicknessValues[idx].total.toFixed(1)} cm`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'سنوات الخدمة' : 'Service Years'
                            }
                        },
                        y: {
                            stacked: true,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'السماكة (سم)' : 'Thickness (cm)'
                            }
                        }
                    }
                }
            });
        }
        
        // Analyze mix
        function analyzeMix() {
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const trafficLevelEl = document.getElementById('traffic-level');
            if (!trafficLevelEl) return;
            
            const trafficLevel = trafficLevelEl.value;
            const serviceTempEl = document.getElementById('service-temp');
            if (!serviceTempEl) return;
            
            const serviceTemp = parseFloat(serviceTempEl.value) || 25;
            
            // Calculate optimum asphalt content based on mix type and traffic
            let optimumAsphalt;
            if (mixType === 'surface') {
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 5.2 : 5.5;
            } else if (mixType === 'binder') {
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 4.8 : 5.0;
            } else { // base
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 4.0 : 4.3;
            }
            
            // Adjust for temperature
            if (serviceTemp > 40) {
                optimumAsphalt -= 0.3;
            } else if (serviceTemp < 10) {
                optimumAsphalt += 0.2;
            }
            
            // Air voids
            let airVoids;
            if (trafficLevel === 'very-high') {
                airVoids = 4.0;
            } else if (trafficLevel === 'high') {
                airVoids = 4.5;
            } else if (trafficLevel === 'medium') {
                airVoids = 5.0;
            } else {
                airVoids = 5.5;
            }
            
            // Theoretical max density (arbitrary calculation for demo)
            const maxDensity = 2.4 + (Math.random() * 0.2); 
            
            // Marshall stability (arbitrary calculation for demo)
            let stabilityBase;
            if (mixType === 'surface') {
                stabilityBase = 1200;
            } else if (mixType === 'binder') {
                stabilityBase = 1000;
            } else {
                stabilityBase = 800;
            }
            
            // Adjust for traffic
            if (trafficLevel === 'very-high') {
                stabilityBase *= 1.3;
            } else if (trafficLevel === 'high') {
                stabilityBase *= 1.15;
            } else if (trafficLevel === 'medium') {
                stabilityBase *= 1.0;
            } else {
                stabilityBase *= 0.9;
            }
            
            // Flow value
            const flowValue = trafficLevel === 'high' || trafficLevel === 'very-high' ? '2-3.5' : '2-4';
            
            // Moisture resistance
            let moistureResistance;
            if (mixType === 'surface') {
                moistureResistance = '80-90%';
            } else {
                moistureResistance = '75-85%';
            }
            
            // Rutting resistance
            let ruttingResistance;
            if (trafficLevel === 'very-high') {
                ruttingResistance = currentLanguage === 'ar' ? 'ممتاز' : 'Excellent';
            } else if (trafficLevel === 'high') {
                ruttingResistance = currentLanguage === 'ar' ? 'جيد جدًا' : 'Very Good';
            } else if (trafficLevel === 'medium') {
                ruttingResistance = currentLanguage === 'ar' ? 'جيد' : 'Good';
            } else {
                ruttingResistance = currentLanguage === 'ar' ? 'مقبول' : 'Acceptable';
            }
            
            // Update results
            const optimumAsphaltEl = document.getElementById('optimum-asphalt');
            const airVoidsEl = document.getElementById('air-voids');
            const maxDensityEl = document.getElementById('max-density');
            const marshallStabilityEl = document.getElementById('marshall-stability');
            const flowValueEl = document.getElementById('flow-value');
            const moistureResistanceEl = document.getElementById('moisture-resistance');
            const ruttingResistanceEl = document.getElementById('rutting-resistance');
            
            if (optimumAsphaltEl) optimumAsphaltEl.textContent = `${optimumAsphalt.toFixed(1)}%`;
            if (airVoidsEl) airVoidsEl.textContent = `${airVoids.toFixed(1)}%`;
            if (maxDensityEl) maxDensityEl.textContent = `${maxDensity.toFixed(3)} g/cm³`;
            if (marshallStabilityEl) marshallStabilityEl.textContent = `${stabilityBase.toFixed(0)} N`;
            if (flowValueEl) flowValueEl.textContent = `${flowValue} mm`;
            if (moistureResistanceEl) moistureResistanceEl.textContent = moistureResistance;
            if (ruttingResistanceEl) ruttingResistanceEl.textContent = ruttingResistance;
            
            // Generate recommendations
            generateRecommendations();
            
            // Update chart
            updateGradationChart();
        }
        
        // Generate recommendations
        function generateRecommendations() {
            const recommendationsEl = document.getElementById('recommendations');
            if (!recommendationsEl) return;
            
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            const trafficLevelEl = document.getElementById('traffic-level');
            if (!trafficLevelEl) return;
            
            const trafficLevel = trafficLevelEl.value;
            
            const serviceTempEl = document.getElementById('service-temp');
            if (!serviceTempEl) return;
            
            const serviceTemp = parseFloat(serviceTempEl.value) || 40;
            
            let recommendations = '';
            let hasRecommendations = false;
            
            // Check sieve conformity
            sieves.forEach((sieve, index) => {
                const input = document.getElementById(sieve.id);
                if (!input || !input.value) return;
                
                const value = parseFloat(input.value);
                if (value < limits[index].min) {
                    hasRecommendations = true;
                    const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                    const diff = (limits[index].min - value).toFixed(1);
                    
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• زيادة المار من المنخل ${sieveName} بنسبة ${diff}% ليصبح ضمن الحدود المسموحة.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Increase passing through sieve ${sieveName} by ${diff}% to be within limits.</p>`;
                    }
                } else if (value > limits[index].max) {
                    hasRecommendations = true;
                    const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                    const diff = (value - limits[index].max).toFixed(1);
                    
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• تقليل المار من المنخل ${sieveName} بنسبة ${diff}% ليصبح ضمن الحدود المسموحة.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Decrease passing through sieve ${sieveName} by ${diff}% to be within limits.</p>`;
                    }
                }
            });
            
            // Special recommendations based on conditions
            
            // Traffic level recommendations
            if (trafficLevel === 'high' || trafficLevel === 'very-high') {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• للحركة المرورية العالية، نصح باستخدام زفت معدّل بالبوليمر (PMA) لزيادة مقاومة التخدد ومقاومة الإجهاد.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• For high traffic, it is recommended to use polymer-modified asphalt (PMA) to increase rutting resistance and stress resistance.</p>`;
                }
            }
            
            // Temperature recommendations
            if (serviceTemp > 45) {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• نظرًا لدرجة الحرارة العالية (${serviceTemp}°C)، نصح باستخدام زفت عالي الثبات (PG 76-10 أو أعلى) مع مضافات لتحسين مقاومة التخدد.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• Due to high temperature (${serviceTemp}°C), it is recommended to use high-stability asphalt (PG 76-10 or higher) with additives to improve rutting resistance.</p>`;
                }
            } else if (serviceTemp < 10) {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• نظرًا لدرجة الحرارة المنخفضة (${serviceTemp}°C)، نصح باستخدام زفت مرن في درجات الحرارة المنخفضة (PG XX-28 أو أقل) لتجنب التشققات الحرارية.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• Due to low temperature (${serviceTemp}°C), it is recommended to use flexible asphalt at low temperatures (PG XX-28 or lower) to avoid thermal cracking.</p>`;
                }
            }
            
            // Add mineral filler recommendation if fine material is low
            const fineInput = document.getElementById('sieve-10');
            if (fineInput && fineInput.value) {
                const fineValue = parseFloat(fineInput.value);
                const fineLimit = limits[9].min;
                if (fineValue < fineLimit) {
                    hasRecommendations = true;
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• زيادة نسبة المالئ المعدني (الفيلر) لتحسين ثبات الخلطة ومقاومتها للماء.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Increase the percentage of mineral filler to improve mix stability and water resistance.</p>`;
                    }
                }
            }
            
            // If no specific recommendations, add general one
            if (!hasRecommendations) {
                if (currentLanguage === 'ar') {
                    recommendations = `<p>التدرج المنخلي مطابق للمواصفات. لا توجد توصيات محددة للتحسين.</p>`;
                } else {
                    recommendations = `<p>The gradation conforms to specifications. No specific improvements needed.</p>`;
                }
            }
            
            recommendationsEl.innerHTML = recommendations;
        }
        
        // Update gradation chart
        function updateGradationChart() {
            const gradationChartEl = document.getElementById('gradation-chart');
            if (!gradationChartEl) return;
            
            const ctx = gradationChartEl.getContext('2d');
            if (!ctx) return;
            
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            // Collect data
            const labels = sieves.map(sieve => currentLanguage === 'ar' ? sieve.sizeAr : sieve.size);
            const minValues = limits.map(limit => limit.min);
            const maxValues = limits.map(limit => limit.max);
            
            // Get user values
            const userValues = sieves.map(sieve => {
                const input = document.getElementById(sieve.id);
                return input && input.value ? parseFloat(input.value) : null;
            });
            
            // Check if we have any user values
            const hasUserValues = userValues.some(val => val !== null);
            
            // Set chart colors
            const textColor = '#333333';
            const gridColor = 'rgba(0, 0, 0, 0.1)';
            
            // If chart exists, destroy it
            if (gradationChart) {
                gradationChart.destroy();
            }
            
            // Create new chart
            gradationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: currentLanguage === 'ar' ? 'الحد الأدنى' : 'Min Limit',
                            data: minValues,
                            borderColor: '#FF6384',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            borderWidth: 2,
                            pointRadius: 3,
                            fill: false
                        },
                        {
                            label: currentLanguage === 'ar' ? 'الحد الأعلى' : 'Max Limit',
                            data: maxValues,
                            borderColor: '#36A2EB',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderWidth: 2,
                            pointRadius: 3,
                            fill: false
                        },
                        {
                            label: currentLanguage === 'ar' ? 'التدرج المستخدم' : 'Used Gradation',
                            data: userValues,
                            borderColor: '#4BC0C0',
                            backgroundColor: 'rgba(75, 192, 192, 0.4)',
                            borderWidth: 3,
                            pointRadius: 5,
                            fill: false,
                            hidden: !hasUserValues
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: currentLanguage === 'ar' ? 'منحنى التدرج المنخلي' : 'Sieve Gradation Curve',
                            font: {
                                size: 16
                            },
                            color: textColor
                        },
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: textColor
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'نسبة المار (%)' : 'Passing (%)',
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'المناخل' : 'Sieves',
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // Generate report
        function generateReport() {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;
            
            // Get all values
            const projectName = document.getElementById('project-name')?.value || '-';
            const projectLocation = document.getElementById('project-location')?.value || '-';
            const cbrValue = document.getElementById('cbr-value')?.value || '6';
            const designLife = document.getElementById('design-life')?.value || '20';
            const trafficGrowth = document.getElementById('traffic-growth')?.value || '3';
            const reliabilityFactor = document.getElementById('reliability-factor')?.value || '90';
            
            // Thickness values
            const surfaceThickness = document.getElementById('surface-thickness')?.textContent || '--';
            const binderThickness = document.getElementById('binder-thickness')?.textContent || '--';
            const baseThickness = document.getElementById('base-thickness')?.textContent || '--';
            const totalThickness = document.getElementById('total-thickness')?.textContent || '--';
            const structuralNumber = document.getElementById('structural-number')?.textContent || '--';
            
            // Percentages
            const surfacePercentage = document.getElementById('surface-percentage')?.textContent || '--';
            const binderPercentage = document.getElementById('binder-percentage')?.textContent || '--';
            const basePercentage = document.getElementById('base-percentage')?.textContent || '--';
            
            // Mix analysis values
            const optimumAsphalt = document.getElementById('optimum-asphalt')?.textContent || '--';
            const airVoids = document.getElementById('air-voids')?.textContent || '--';
            const maxDensity = document.getElementById('max-density')?.textContent || '--';
            const gradationConformity = document.getElementById('gradation-conformity')?.textContent || '--';
            
            // Get thickness recommendations
            const thicknessRecommendation = document.querySelector('#thickness-recommendations .bg-gray-50')?.innerHTML || '';
            
            const recommendations = document.getElementById('recommendations');
            if (!recommendations) return;
            
            const mixRecommendations = recommendations.innerHTML || '';
            
            // Format current date in standard Gregorian format (YYYY-MM-DD)
            const today = new Date();
            const dateString = today.toISOString().split('T')[0];
            
            // Create the report content with improved styling
            let reportHtml = '';
            
            if (currentLanguage === 'ar') {
                reportHtml = `
                <div class="print-white" id="report-printable" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: white; color: black;">
                    <!-- Header Section -->
                    <div class="text-center mb-6 print-no-break" style="border-bottom: 3px solid #2563eb; padding-bottom: 15px; margin-bottom: 25px;">
                        <h1 style="font-size: 24px; font-weight: bold; color: #1e40af; margin-bottom: 8px; text-align: center;">تقرير حساب سماكة طبقات الرصف وتحليل الخلطة الإسفلتية</h1>
                        <p style="font-size: 14px; color: #6b7280; margin: 0; text-align: center;">تاريخ التقرير: ${dateString}</p>
                        <p style="font-size: 12px; color: #9ca3af; margin: 5px 0 0 0; text-align: center;">صُمم بحب لجميع المهندسين من قبل المهندس محمد يونس الجوالي</p>
                    </div>
                    
                    <!-- Project Information Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">معلومات المشروع</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; width: 35%; border-right: 1px solid #e2e8f0;">اسم المشروع:</td>
                                    <td style="padding: 8px 12px;">${projectName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">الموقع:</td>
                                    <td style="padding: 8px 12px;">${projectLocation}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">قيمة CBR:</td>
                                    <td style="padding: 8px 12px;">${cbrValue}%</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">العمر الافتراضي:</td>
                                    <td style="padding: 8px 12px;">${designLife} سنة</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">نسبة زيادة حركة المرور:</td>
                                    <td style="padding: 8px 12px;">${trafficGrowth}%</td>
                                </tr>
                                <tr>
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">معامل الموثوقية:</td>
                                    <td style="padding: 8px 12px;">${reliabilityFactor}%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Thickness Results Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">نتائج حساب سماكة الطبقات</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #1e40af; color: white;">
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">الطبقة</th>
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">السماكة</th>
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">النسبة المئوية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">طبقة السطح</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${surfaceThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${surfacePercentage}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">طبقة الرابطة</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${binderThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${binderPercentage}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">طبقة الأساس</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${baseThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${basePercentage}</td>
                                    </tr>
                                    <tr style="background: #dbeafe; font-weight: bold;">
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">إجمالي السماكة</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${totalThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">100%</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div style="margin-top: 15px; padding: 10px; background: #eff6ff; border-radius: 6px; border-left: 4px solid #2563eb;">
                                <p style="margin: 0; font-weight: 600; color: #1e40af;">رقم البنية الإنشائية (SN): ${structuralNumber}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mix Analysis Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">نتائج تحليل الخلطة الإسفلتية</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; width: 50%; border-right: 1px solid #e2e8f0;">النسبة المثلى للزفت:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${optimumAsphalt}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">الفراغات الهوائية:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${airVoids}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">الكثافة النظرية القصوى:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${maxDensity}</td>
                                </tr>
                                <tr>
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">نسبة مطابقة التدرج:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${gradationConformity}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Thickness Recommendations Section -->
                    ${thicknessRecommendation ? `
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">توصيات سماكة الطبقات</h2>
                        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #f59e0b; border-left: 4px solid #f59e0b;">
                            ${thicknessRecommendation}
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- Mix Recommendations Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">توصيات تحسين الخلطة</h2>
                        <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; border: 1px solid #10b981; border-left: 4px solid #10b981;">
                            ${mixRecommendations}
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; color: #6b7280; font-size: 12px;">
                        <p style="margin: 0;">تطبيق حساب سماكة طبقات الرصف وتحليل وتصميم الخلطات الاسفلتية © 2025-2026</p>
                        <p style="margin: 5px 0 0 0;">صُمم بحب لجميع المهندسين من قبل المهندس محمد يونس الجوالي</p>
                    </div>
                </div>
                `;
            } else {
                reportHtml = `
                <div class="print-white" id="report-printable" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: white; color: black;">
                    <!-- Header Section -->
                    <div class="text-center mb-6 print-no-break" style="border-bottom: 3px solid #2563eb; padding-bottom: 15px; margin-bottom: 25px;">
                        <h1 style="font-size: 24px; font-weight: bold; color: #1e40af; margin-bottom: 8px; text-align: center;">Pavement Thickness Calculation and Asphalt Mix Design Report</h1>
                        <p style="font-size: 14px; color: #6b7280; margin: 0; text-align: center;">Report Date: ${dateString}</p>
                        <p style="font-size: 12px; color: #9ca3af; margin: 5px 0 0 0; text-align: center;">Designed with love for all engineers by Engineer Mohammed Younis Al-Jawali</p>
                    </div>
                    
                    <!-- Project Information Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">Project Information</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; width: 35%; border-right: 1px solid #e2e8f0;">Project Name:</td>
                                    <td style="padding: 8px 12px;">${projectName}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Location:</td>
                                    <td style="padding: 8px 12px;">${projectLocation}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">CBR Value:</td>
                                    <td style="padding: 8px 12px;">${cbrValue}%</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Design Life:</td>
                                    <td style="padding: 8px 12px;">${designLife} years</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Traffic Growth Rate:</td>
                                    <td style="padding: 8px 12px;">${trafficGrowth}%</td>
                                </tr>
                                <tr>
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Reliability Factor:</td>
                                    <td style="padding: 8px 12px;">${reliabilityFactor}%</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Thickness Results Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">Pavement Thickness Results</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #1e40af; color: white;">
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">Layer</th>
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">Thickness</th>
                                        <th style="padding: 10px; text-align: center; border: 1px solid #1e40af;">Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">Surface Layer</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${surfaceThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${surfacePercentage}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">Binder Layer</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${binderThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${binderPercentage}</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #e2e8f0;">
                                        <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; text-align: center; border: 1px solid #e2e8f0;">Base Layer</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${baseThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${basePercentage}</td>
                                    </tr>
                                    <tr style="background: #dbeafe; font-weight: bold;">
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">Total Thickness</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">${totalThickness}</td>
                                        <td style="padding: 8px 12px; text-align: center; border: 1px solid #e2e8f0;">100%</td>
                                    </tr>
                                </tbody>
                            </table>
                            <div style="margin-top: 15px; padding: 10px; background: #eff6ff; border-radius: 6px; border-left: 4px solid #2563eb;">
                                <p style="margin: 0; font-weight: 600; color: #1e40af;">Structural Number (SN): ${structuralNumber}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mix Analysis Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">Asphalt Mix Analysis Results</h2>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid #e2e8f0;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; width: 50%; border-right: 1px solid #e2e8f0;">Optimum Asphalt Content:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${optimumAsphalt}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Air Voids:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${airVoids}</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Theoretical Maximum Density:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${maxDensity}</td>
                                </tr>
                                <tr>
                                    <td style="font-weight: 600; padding: 8px 12px; background: #f1f5f9; border-right: 1px solid #e2e8f0;">Gradation Conformity:</td>
                                    <td style="padding: 8px 12px; font-weight: 600; color: #059669;">${gradationConformity}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Thickness Recommendations Section -->
                    ${thicknessRecommendation ? `
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">Thickness Recommendations</h2>
                        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #f59e0b; border-left: 4px solid #f59e0b;">
                            ${thicknessRecommendation}
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- Mix Recommendations Section -->
                    <div style="margin-bottom: 25px;" class="print-no-break">
                        <h2 style="font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 15px; padding: 8px 0; border-bottom: 2px solid #e5e7eb;">Mix Improvement Recommendations</h2>
                        <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; border: 1px solid #10b981; border-left: 4px solid #10b981;">
                            ${mixRecommendations}
                        </div>
                    </div>
                    
                    <!-- Footer -->
                    <div style="text-align: center; margin-top: 30px; padding-top: 15px; border-top: 2px solid #e5e7eb; color: #6b7280; font-size: 12px;">
                        <p style="margin: 0;">Pavement Thickness Calculation and Asphalt Mix Design App © 2025-2026</p>
                        <p style="margin: 5px 0 0 0;">Designed with love for all engineers by Engineer Mohammed Younis Al-Jawali</p>
                    </div>
                </div>
                `;
            }
            
            reportContent.innerHTML = reportHtml;
        }
        
        // Update thickness chart (showing relationship between thickness and service years)
        function updateThicknessChart(surfaceThickness, binderThickness, baseThickness) {
            const thicknessChartEl = document.getElementById('thickness-chart');
            if (!thicknessChartEl) return;
            
            const ctx = thicknessChartEl.getContext('2d');
            if (!ctx) return;
            
            // Calculate thicknesses for different service years
            const serviceYears = [5, 10, 15, 20, 25, 30];
            const thicknessValues = serviceYears.map(years => {
                const designLifeEl = document.getElementById('design-life');
                if (!designLifeEl) return { surface: 0, binder: 0, base: 0, total: 0 };
                
                const originalDesignLife = parseFloat(designLifeEl.value) || 20;
                const scaleFactor = Math.pow(years / originalDesignLife, 0.25); // Use a power function to model relationship
                
                return {
                    surface: surfaceThickness * scaleFactor,
                    binder: binderThickness * scaleFactor,
                    base: baseThickness * scaleFactor,
                    total: (surfaceThickness + binderThickness + baseThickness) * scaleFactor
                };
            });
            
            // If chart exists, destroy it
            if (thicknessChart) {
                thicknessChart.destroy();
            }
            
            // Create new chart
            thicknessChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: serviceYears.map(year => `${year} ${currentLanguage === 'ar' ? 'سنة' : 'years'}`),
                    datasets: [
                        {
                            label: currentLanguage === 'ar' ? 'طبقة السطح' : 'Surface Layer',
                            data: thicknessValues.map(t => t.surface),
                            backgroundColor: '#FF6384',
                            borderColor: '#FF6384',
                            borderWidth: 1
                        },
                        {
                            label: currentLanguage === 'ar' ? 'طبقة الرابطة' : 'Binder Layer',
                            data: thicknessValues.map(t => t.binder),
                            backgroundColor: '#36A2EB',
                            borderColor: '#36A2EB',
                            borderWidth: 1
                        },
                        {
                            label: currentLanguage === 'ar' ? 'طبقة الأساس' : 'Base Layer',
                            data: thicknessValues.map(t => t.base),
                            backgroundColor: '#4BC0C0',
                            borderColor: '#4BC0C0',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: currentLanguage === 'ar' ? 'سماكة الطبقات مقابل سنوات الخدمة' : 'Layer Thickness vs Service Years',
                            font: {
                                size: 16
                            },
                        },
                        legend: {
                            position: 'bottom',
                        },
                        tooltip: {
                            callbacks: {
                                afterBody: function(tooltipItems) {
                                    const idx = tooltipItems[0].dataIndex;
                                    return `${currentLanguage === 'ar' ? 'إجمالي السماكة' : 'Total Thickness'}: ${thicknessValues[idx].total.toFixed(1)} cm`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'سنوات الخدمة' : 'Service Years'
                            }
                        },
                        y: {
                            stacked: true,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'السماكة (سم)' : 'Thickness (cm)'
                            }
                        }
                    }
                }
            });
        }
        
        // Analyze mix
        function analyzeMix() {
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const trafficLevelEl = document.getElementById('traffic-level');
            if (!trafficLevelEl) return;
            
            const trafficLevel = trafficLevelEl.value;
            const serviceTempEl = document.getElementById('service-temp');
            if (!serviceTempEl) return;
            
            const serviceTemp = parseFloat(serviceTempEl.value) || 25;
            
            // Calculate optimum asphalt content based on mix type and traffic
            let optimumAsphalt;
            if (mixType === 'surface') {
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 5.2 : 5.5;
            } else if (mixType === 'binder') {
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 4.8 : 5.0;
            } else { // base
                optimumAsphalt = trafficLevel === 'high' || trafficLevel === 'very-high' ? 4.0 : 4.3;
            }
            
            // Adjust for temperature
            if (serviceTemp > 40) {
                optimumAsphalt -= 0.3;
            } else if (serviceTemp < 10) {
                optimumAsphalt += 0.2;
            }
            
            // Air voids
            let airVoids;
            if (trafficLevel === 'very-high') {
                airVoids = 4.0;
            } else if (trafficLevel === 'high') {
                airVoids = 4.5;
            } else if (trafficLevel === 'medium') {
                airVoids = 5.0;
            } else {
                airVoids = 5.5;
            }
            
            // Theoretical max density (arbitrary calculation for demo)
            const maxDensity = 2.4 + (Math.random() * 0.2); 
            
            // Marshall stability (arbitrary calculation for demo)
            let stabilityBase;
            if (mixType === 'surface') {
                stabilityBase = 1200;
            } else if (mixType === 'binder') {
                stabilityBase = 1000;
            } else {
                stabilityBase = 800;
            }
            
            // Adjust for traffic
            if (trafficLevel === 'very-high') {
                stabilityBase *= 1.3;
            } else if (trafficLevel === 'high') {
                stabilityBase *= 1.15;
            } else if (trafficLevel === 'medium') {
                stabilityBase *= 1.0;
            } else {
                stabilityBase *= 0.9;
            }
            
            // Flow value
            const flowValue = trafficLevel === 'high' || trafficLevel === 'very-high' ? '2-3.5' : '2-4';
            
            // Moisture resistance
            let moistureResistance;
            if (mixType === 'surface') {
                moistureResistance = '80-90%';
            } else {
                moistureResistance = '75-85%';
            }
            
            // Rutting resistance
            let ruttingResistance;
            if (trafficLevel === 'very-high') {
                ruttingResistance = currentLanguage === 'ar' ? 'ممتاز' : 'Excellent';
            } else if (trafficLevel === 'high') {
                ruttingResistance = currentLanguage === 'ar' ? 'جيد جدًا' : 'Very Good';
            } else if (trafficLevel === 'medium') {
                ruttingResistance = currentLanguage === 'ar' ? 'جيد' : 'Good';
            } else {
                ruttingResistance = currentLanguage === 'ar' ? 'مقبول' : 'Acceptable';
            }
            
            // Update results
            const optimumAsphaltEl = document.getElementById('optimum-asphalt');
            const airVoidsEl = document.getElementById('air-voids');
            const maxDensityEl = document.getElementById('max-density');
            const marshallStabilityEl = document.getElementById('marshall-stability');
            const flowValueEl = document.getElementById('flow-value');
            const moistureResistanceEl = document.getElementById('moisture-resistance');
            const ruttingResistanceEl = document.getElementById('rutting-resistance');
            
            if (optimumAsphaltEl) optimumAsphaltEl.textContent = `${optimumAsphalt.toFixed(1)}%`;
            if (airVoidsEl) airVoidsEl.textContent = `${airVoids.toFixed(1)}%`;
            if (maxDensityEl) maxDensityEl.textContent = `${maxDensity.toFixed(3)} g/cm³`;
            if (marshallStabilityEl) marshallStabilityEl.textContent = `${stabilityBase.toFixed(0)} N`;
            if (flowValueEl) flowValueEl.textContent = `${flowValue} mm`;
            if (moistureResistanceEl) moistureResistanceEl.textContent = moistureResistance;
            if (ruttingResistanceEl) ruttingResistanceEl.textContent = ruttingResistance;
            
            // Generate recommendations
            generateRecommendations();
            
            // Update chart
            updateGradationChart();
        }
        
        // Generate recommendations
        function generateRecommendations() {
            const recommendationsEl = document.getElementById('recommendations');
            if (!recommendationsEl) return;
            
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            const trafficLevelEl = document.getElementById('traffic-level');
            if (!trafficLevelEl) return;
            
            const trafficLevel = trafficLevelEl.value;
            
            const serviceTempEl = document.getElementById('service-temp');
            if (!serviceTempEl) return;
            
            const serviceTemp = parseFloat(serviceTempEl.value) || 40;
            
            let recommendations = '';
            let hasRecommendations = false;
            
            // Check sieve conformity
            sieves.forEach((sieve, index) => {
                const input = document.getElementById(sieve.id);
                if (!input || !input.value) return;
                
                const value = parseFloat(input.value);
                if (value < limits[index].min) {
                    hasRecommendations = true;
                    const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                    const diff = (limits[index].min - value).toFixed(1);
                    
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• زيادة المار من المنخل ${sieveName} بنسبة ${diff}% ليصبح ضمن الحدود المسموحة.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Increase passing through sieve ${sieveName} by ${diff}% to be within limits.</p>`;
                    }
                } else if (value > limits[index].max) {
                    hasRecommendations = true;
                    const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                    const diff = (value - limits[index].max).toFixed(1);
                    
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• تقليل المار من المنخل ${sieveName} بنسبة ${diff}% ليصبح ضمن الحدود المسموحة.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Decrease passing through sieve ${sieveName} by ${diff}% to be within limits.</p>`;
                    }
                }
            });
            
            // Special recommendations based on conditions
            
            // Traffic level recommendations
            if (trafficLevel === 'high' || trafficLevel === 'very-high') {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• للحركة المرورية العالية، نصح باستخدام زفت معدّل بالبوليمر (PMA) لزيادة مقاومة التخدد ومقاومة الإجهاد.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• For high traffic, it is recommended to use polymer-modified asphalt (PMA) to increase rutting resistance and stress resistance.</p>`;
                }
            }
            
            // Temperature recommendations
            if (serviceTemp > 45) {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• نظرًا لدرجة الحرارة العالية (${serviceTemp}°C)، نصح باستخدام زفت عالي الثبات (PG 76-10 أو أعلى) مع مضافات لتحسين مقاومة التخدد.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• Due to high temperature (${serviceTemp}°C), it is recommended to use high-stability asphalt (PG 76-10 or higher) with additives to improve rutting resistance.</p>`;
                }
            } else if (serviceTemp < 10) {
                hasRecommendations = true;
                if (currentLanguage === 'ar') {
                    recommendations += `<p class="mb-2">• نظرًا لدرجة الحرارة المنخفضة (${serviceTemp}°C)، نصح باستخدام زفت مرن في درجات الحرارة المنخفضة (PG XX-28 أو أقل) لتجنب التشققات الحرارية.</p>`;
                } else {
                    recommendations += `<p class="mb-2">• Due to low temperature (${serviceTemp}°C), it is recommended to use flexible asphalt at low temperatures (PG XX-28 or lower) to avoid thermal cracking.</p>`;
                }
            }
            
            // Add mineral filler recommendation if fine material is low
            const fineInput = document.getElementById('sieve-10');
            if (fineInput && fineInput.value) {
                const fineValue = parseFloat(fineInput.value);
                const fineLimit = limits[9].min;
                if (fineValue < fineLimit) {
                    hasRecommendations = true;
                    if (currentLanguage === 'ar') {
                        recommendations += `<p class="mb-2">• زيادة نسبة المالئ المعدني (الفيلر) لتحسين ثبات الخلطة ومقاومتها للماء.</p>`;
                    } else {
                        recommendations += `<p class="mb-2">• Increase the percentage of mineral filler to improve mix stability and water resistance.</p>`;
                    }
                }
            }
            
            // If no specific recommendations, add general one
            if (!hasRecommendations) {
                if (currentLanguage === 'ar') {
                    recommendations = `<p>التدرج المنخلي مطابق للمواصفات. لا توجد توصيات محددة للتحسين.</p>`;
                } else {
                    recommendations = `<p>The gradation conforms to specifications. No specific improvements needed.</p>`;
                }
            }
            
            recommendationsEl.innerHTML = recommendations;
        }
        
        // Update gradation chart
        function updateGradationChart() {
            const gradationChartEl = document.getElementById('gradation-chart');
            if (!gradationChartEl) return;
            
            const ctx = gradationChartEl.getContext('2d');
            if (!ctx) return;
            
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            if (!mixTypeEl) return;
            
            const mixType = mixTypeEl.value;
            const specTypeEl = document.getElementById('spec-type');
            if (!specTypeEl) return;
            
            const specType = specTypeEl.value;
            const limits = specLimits[mixType][specType];
            
            // Collect data
            const labels = sieves.map(sieve => currentLanguage === 'ar' ? sieve.sizeAr : sieve.size);
            const minValues = limits.map(limit => limit.min);
            const maxValues = limits.map(limit => limit.max);
            
            // Get user values
            const userValues = sieves.map(sieve => {
                const input = document.getElementById(sieve.id);
                return input && input.value ? parseFloat(input.value) : null;
            });
            
            // Check if we have any user values
            const hasUserValues = userValues.some(val => val !== null);
            
            // Set chart colors
            const textColor = '#333333';
            const gridColor = 'rgba(0, 0, 0, 0.1)';
            
            // If chart exists, destroy it
            if (gradationChart) {
                gradationChart.destroy();
            }
            
            // Create new chart
            gradationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: currentLanguage === 'ar' ? 'الحد الأدنى' : 'Min Limit',
                            data: minValues,
                            borderColor: '#FF6384',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            borderWidth: 2,
                            pointRadius: 3,
                            fill: false
                        },
                        {
                            label: currentLanguage === 'ar' ? 'الحد الأعلى' : 'Max Limit',
                            data: maxValues,
                            borderColor: '#36A2EB',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderWidth: 2,
                            pointRadius: 3,
                            fill: false
                        },
                        {
                            label: currentLanguage === 'ar' ? 'التدرج المستخدم' : 'Used Gradation',
                            data: userValues,
                            borderColor: '#4BC0C0',
                            backgroundColor: 'rgba(75, 192, 192, 0.4)',
                            borderWidth: 3,
                            pointRadius: 5,
                            fill: false,
                            hidden: !hasUserValues
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: currentLanguage === 'ar' ? 'منحنى التدرج المنخلي' : 'Sieve Gradation Curve',
                            font: {
                                size: 16
                            },
                            color: textColor
                        },
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: textColor
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'نسبة المار (%)' : 'Passing (%)',
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: currentLanguage === 'ar' ? 'المناخل' : 'Sieves',
                                color: textColor
                            },
                            grid: {
                                color: gridColor
                            },
                            ticks: {
                                color: textColor
                            }
                        }
                    }
                }
            });
        }

        // Generate report
        function generateReport() {
            const reportContent = document.getElementById('report-content');
            if (!reportContent) return;
            
            // Get all values
            const projectName = document.getElementById('project-name')?.value || '-';
            const projectLocation = document.getElementById('project-location')?.value || '-';
            const cbrValue = document.getElementById('cbr-value')?.value || '6';
            const designLife = document.getElementById('design-life')?.value || '20';
            const trafficGrowth = document.getElementById('traffic-growth')?.value || '3';
            const reliabilityFactor = document.getElementById('reliability-factor')?.value || '90';
            
            // Thickness values
            const surfaceThickness = document.getElementById('surface-thickness')?.textContent || '--';
            const binderThickness = document.getElementById('binder-thickness')?.textContent || '--';
            const baseThickness = document.getElementById('base-thickness')?.textContent || '--';
            const totalThickness = document.getElementById('total-thickness')?.textContent || '--';
            const structuralNumber = document.getElementById('structural-number')?.textContent || '--';
            
            // Percentages
            const surfacePercentage = document.getElementById('surface-percentage')?.textContent || '--';
            const binderPercentage = document.getElementById('binder-percentage')?.textContent || '--';
            const basePercentage = document.getElementById('base-percentage')?.textContent || '--';
            
            // Mix analysis values
            const optimumAsphalt = document.getElementById('optimum-asphalt')?.textContent || '--';
            const airVoids = document.getElementById('air-voids')?.textContent || '--';
            const maxDensity = document.getElementById('max-density')?.textContent || '--';
            const gradationConformity = document.getElementById('gradation-conformity')?.textContent || '--';
            
            // Get thickness recommendations
            const thicknessRecommendation = document.querySelector('#thickness-recommendations .bg-gray-50')?.innerHTML || '';
            
            const recommendations = document.getElementById('recommendations');
            if (!recommendations) return;
            
            const mixRecommendations = recommendations.innerHTML || '';
            
            // Format current date in standard Gregorian format (YYYY-MM-DD)
            const today = new Date();
            const dateString = today.toISOString().split('T')[0];
            
            // Create the report content with improved styling
            let reportHtml = '';
            
            if (currentLanguage === 'ar') {
                reportHtml = `
                <div class="print-white" id="report-printable" style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: white; color: black;">
                    <!-- Header Section -->
                    <div class="text-center mb-6 print-no-break" style="border-bottom: 3px solid #2563eb; padding-bottom: 15px; margin-bottom: 25px;">
                        <h1 style="font-size: 24px; font-weight: bold; color: #1e40af; margin-bottom: 8px; text-align: center;">تقرير حساب سماكة طبقات الرصف وتحليل الخلطة الإسفلتية</h1>
                        <p style="font-size
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">توصيات تحسين الخلطة</h2>
                        <div class="p-2 border print-border rounded-lg bg-gray-50 print-small-text">
                            ${mixRecommendations}
                        </div>
                    </div>
                </div>
                `;
            } else {
                reportHtml = `
                <div class="print-white" id="report-printable">
                    <div class="text-center mb-4 print-no-break">
                        <h1 class="text-xl font-bold mb-1 print-title">Asphalt Mix Design Report</h1>
                        <p class="text-sm text-gray-600 print-text">Report Date: ${dateString}</p>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Project Information</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-2/5 print-small-text">Project Name:</td>
                                <td class="p-1 print-small-text">${projectName}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Location:</td>
                                <td class="p-1 print-small-text">${projectLocation}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Soil Type:</td>
                                <td class="p-1 print-small-text">${soilType}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">CBR Value:</td>
                                <td class="p-1 print-small-text">${cbrValue}%</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Service Temperature:</td>
                                <td class="p-1 print-small-text">${serviceTemp} °C</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Traffic Level:</td>
                                <td class="p-1 print-small-text">${trafficLevel}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Equivalent Axle Load:</td>
                                <td class="p-1 print-small-text">${esalValue} × 10⁶</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Traffic Growth Rate:</td>
                                <td class="p-1 print-small-text">${trafficGrowth}%</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Design Life:</td>
                                <td class="p-1 print-small-text">${designLife} years</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Reliability Factor:</td>
                                <td class="p-1 print-small-text">${reliabilityFactor}%</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Pavement Thickness</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-1/3 print-small-text">Surface Layer:</td>
                                <td class="p-1 print-small-text">${surfaceThickness}</td>
                                <td class="p-1 print-small-text">${surfacePercentage}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Binder Layer:</td>
                                <td class="p-1 print-small-text">${binderThickness}</td>
                                <td class="p-1 print-small-text">${binderPercentage}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Base Layer:</td>
                                <td class="p-1 print-small-text">${baseThickness}</td>
                                <td class="p-1 print-small-text">${basePercentage}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Total Thickness:</td>
                                <td class="p-1 font-medium print-small-text">${totalThickness}</td>
                                <td class="p-1 print-small-text">100%</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Structural Number (SN):</td>
                                <td class="p-1 print-small-text" colspan="2">${structuralNumber}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Elastic Modulus for Layers (MPa)</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-1/3 print-small-text">Surface Layer:</td>
                                <td class="p-1 print-small-text">${surfaceModulus}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Binder Layer:</td>
                                <td class="p-1 print-small-text">${binderModulus}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Base Layer:</td>
                                <td class="p-1 print-small-text">${baseModulus}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Layer Coefficients</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-1/3 print-small-text">Surface Layer Coefficient (a₁):</td>
                                <td class="p-1 print-small-text">${surfaceCoef}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Binder Layer Coefficient (a₂):</td>
                                <td class="p-1 print-small-text">${binderCoef}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Base Layer Coefficient (a₃):</td>
                                <td class="p-1 print-small-text">${baseCoef}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-page-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Mix Information</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-2/5 print-small-text">Mix Type:</td>
                                <td class="p-1 print-small-text">${mixTypeText}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Specification Type:</td>
                                <td class="p-1 print-small-text">${specType}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Conformity Percentage:</td>
                                <td class="p-1 print-small-text">${gradationConformity}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Design Results</h2>
                        <table class="w-full border print-border print-table-compact">
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 w-2/5 print-small-text">Optimum Asphalt Content:</td>
                                <td class="p-1 print-small-text">${optimumAsphalt}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Air Voids:</td>
                                <td class="p-1 print-small-text">${airVoids}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Theoretical Maximum Density:</td>
                                <td class="p-1 print-small-text">${maxDensity}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Marshall Stability:</td>
                                <td class="p-1 print-small-text">${document.getElementById('marshall-stability')?.textContent || '--'}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Flow:</td>
                                <td class="p-1 print-small-text">${document.getElementById('flow-value')?.textContent || '--'}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Moisture Resistance:</td>
                                <td class="p-1 print-small-text">${document.getElementById('moisture-resistance')?.textContent || '--'}</td>
                            </tr>
                            <tr class="border-b print-border">
                                <td class="font-medium p-1 border-r print-border bg-gray-50 print-small-text">Rutting Resistance:</td>
                                <td class="p-1 print-small-text">${document.getElementById('rutting-resistance')?.textContent || '--'}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Sieve Gradation</h2>
                        <table class="w-full border print-border print-table-compact">
                            <thead>
                                <tr class="bg-gray-100 border-b print-border">
                                    <th class="p-1 border-r print-border text-left print-small-text">Sieve</th>
                                    <th class="p-1 border-r print-border text-left print-small-text">Passing (%)</th>
                                    <th class="p-1 border-r print-border text-left print-small-text">Min (%)</th>
                                    <th class="p-1 text-left print-small-text">Max (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${sieves.map((sieve, index) => {
                                    const input = document.getElementById(sieve.id);
                                    const value = input && input.value ? input.value : '-';
                                    const mixType = document.querySelector('input[name="mix-type"]:checked').value;
                                    const specType = document.getElementById('spec-type').value;
                                    const limits = specLimits[mixType][specType];
                                    
                                    return `
                                    <tr class="border-b print-border">
                                        <td class="p-1 border-r print-border print-small-text">${sieve.size} (${sieve.sizeMm} mm)</td>
                                        <td class="p-1 border-r print-border print-small-text">${value}</td>
                                        <td class="p-1 border-r print-border print-small-text">${limits[index].min}</td>
                                        <td class="p-1 print-small-text">${limits[index].max}</td>
                                    </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Thickness Recommendations</h2>
                        <div class="p-2 border print-border rounded-lg bg-gray-50 print-small-text">
                            ${thicknessRecommendation}
                        </div>
                    </div>
                    
                    <div class="mb-4 print-no-break">
                        <h2 class="text-lg font-semibold mb-2 text-primary-600 print-subtitle">Mix Improvement Recommendations</h2>
                        <div class="p-2 border print-border rounded-lg bg-gray-50 print-small-text">
                            ${mixRecommendations}
                        </div>
                    </div>
                </div>
                `;
            }
            
            reportContent.innerHTML = reportHtml;
        }
        
        // Download PDF
        function downloadPDF() {
            // Get report template
            const reportContent = document.getElementById('report-printable');
            
            if (!reportContent) {
                alert(currentLanguage === 'ar' ? 'يرجى إنشاء التقرير أولاً' : 'Please generate the report first');
                return;
            }
            
            if (typeof window.jspdf === 'undefined') {
                alert(currentLanguage === 'ar' ? 'لم يتم تحميل مكتبة jsPDF. يرجى المحاولة مرة أخرى.' : 'jsPDF library not loaded. Please try again.');
                return;
            }
            
            const { jsPDF } = window.jspdf;
            
            // Create new PDF - A4 size with proper orientation
            const pdf = new jsPDF('p', 'mm', 'a4');
            
            // Get project name for the file
            const projectNameEl = document.getElementById('project-name');
            if (!projectNameEl) return;
            
            const projectName = projectNameEl.value || (currentLanguage === 'ar' ? 'خلطة-إسفلتية' : 'asphalt-mix');
            const fileName = `${projectName.replace(/\s+/g, '-')}.pdf`;
            
            // Use html2canvas to convert the report to an image with reduced quality
            html2canvas(reportContent, {
                scale: 1, // Reduced from 2 to 1
                useCORS: true,
                logging: false,
                backgroundColor: '#FFFFFF',
                width: reportContent.scrollWidth,
                height: reportContent.scrollHeight,
                allowTaint: true,
                foreignObjectRendering: false,
                imageTimeout: 0,
                onclone: function(clonedDoc) {
                    // Force the cloned document to use print styles
                    let style = clonedDoc.createElement('style');
                    style.innerHTML = `
                        * { color: black !important; background-color: white !important; }
                        .print-border { border: 1px solid black !important; }
                        .print-title { font-size: 14pt !important; font-weight: bold !important; }
                        .print-subtitle { font-size: 12pt !important; font-weight: bold !important; }
                        .print-text { font-size: 10pt !important; }
                        .print-small-text { font-size: 8pt !important; }
                        .print-page-break { page-break-before: always !important; }
                        .print-no-break { page-break-inside: avoid !important; }
                        table { border-collapse: collapse !important; width: 100% !important; }
                        td, th { border: 1px solid black !important; padding: 2px 4px !important; }
                    `;
                    clonedDoc.head.appendChild(style);
                }
            }).then(canvas => {
                // PDF dimensions
                const imgWidth = 210; // A4 width in mm
                const pageHeight = 297; // A4 height in mm
                const imgHeight = canvas.height * imgWidth / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;
                
                // Convert canvas to compressed image data
                const imgData = canvas.toDataURL('image/jpeg', 0.7); // Use JPEG with 70% quality instead of PNG
                
                // Add image to first page
                pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight, undefined, 'MEDIUM'); // Use MEDIUM compression
                heightLeft -= pageHeight;
                
                // Add new pages if content overflows
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight, undefined, 'MEDIUM');
                    heightLeft -= pageHeight;
                }
                
                // Save the PDF with compression
                pdf.save(fileName, { compress: true });
            }).catch(err => {
                console.error('Error generating PDF:', err);
                alert(currentLanguage === 'ar' ? 'حدث خطأ أثناء إنشاء ملف PDF' : 'Error generating PDF');
            });
        }
        
        // Download Excel (improved with proper encoding and formatting)
        function downloadExcel() {
            const projectNameEl = document.getElementById('project-name');
            if (!projectNameEl) return;
            
            const projectName = projectNameEl.value || (currentLanguage === 'ar' ? 'خلطة-إسفلتية' : 'asphalt-mix');
            const fileName = `${projectName.replace(/\s+/g, '-')}.xlsx`;
            
            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const wsData = [];
            
            // Add project info section
            wsData.push([currentLanguage === 'ar' ? 'معلومات المشروع' : 'Project Information', '']);
            wsData.push(['']);
            
            const projectNameValue = document.getElementById('project-name')?.value || '-';
            wsData.push([currentLanguage === 'ar' ? 'اسم المشروع' : 'Project Name', projectNameValue]);
            
            const projectLocationValue = document.getElementById('project-location')?.value || '-';
            wsData.push([currentLanguage === 'ar' ? 'الموقع' : 'Location', projectLocationValue]);
            
            const cbrValue = document.getElementById('cbr-value')?.value || '6';
            wsData.push([currentLanguage === 'ar' ? 'قيمة CBR' : 'CBR Value', `${cbrValue}%`]);
            
            const designLife = document.getElementById('design-life')?.value || '20';
            wsData.push([currentLanguage === 'ar' ? 'العمر الافتراضي للطريق' : 'Design Life', `${designLife} ${currentLanguage === 'ar' ? 'سنة' : 'years'}`]);
            
            const trafficGrowth = document.getElementById('traffic-growth')?.value || '3';
            wsData.push([currentLanguage === 'ar' ? 'نسبة زيادة حركة المرور' : 'Traffic Growth Rate', `${trafficGrowth}%`]);
            
            wsData.push(['']);
            
            // Add thickness info section
            wsData.push([currentLanguage === 'ar' ? 'سماكة طبقات الرصف' : 'Pavement Thickness', '']);
            wsData.push(['']);
            
            const surfaceThickness = document.getElementById('surface-thickness')?.textContent || '--';
            const surfacePercentage = document.getElementById('surface-percentage')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'طبقة السطح' : 'Surface Layer', surfaceThickness, surfacePercentage]);
            
            const binderThickness = document.getElementById('binder-thickness')?.textContent || '--';
            const binderPercentage = document.getElementById('binder-percentage')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'طبقة الرابطة' : 'Binder Layer', binderThickness, binderPercentage]);
            
            const baseThickness = document.getElementById('base-thickness')?.textContent || '--';
            const basePercentage = document.getElementById('base-percentage')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'طبقة الأساس' : 'Base Layer', baseThickness, basePercentage]);
            
            const totalThickness = document.getElementById('total-thickness')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'إجمالي السماكة' : 'Total Thickness', totalThickness, '100%']);
            
            const structuralNumber = document.getElementById('structural-number')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'رقم البنية الإنشائية (SN)' : 'Structural Number (SN)', structuralNumber, '']);
            
            wsData.push(['']);
            
            // Add elastic modulus section
            wsData.push([currentLanguage === 'ar' ? 'معامل المرونة للطبقات (MPa)' : 'Elastic Modulus for Layers (MPa)', '']);
            wsData.push(['']);
            
            const surfaceModulus = document.getElementById('surface-modulus')?.value || '3000';
            wsData.push([currentLanguage === 'ar' ? 'طبقة السطح' : 'Surface Layer', surfaceModulus]);
            
            const binderModulus = document.getElementById('binder-modulus')?.value || '2500';
            wsData.push([currentLanguage === 'ar' ? 'طبقة الرابطة' : 'Binder Layer', binderModulus]);
            
            const baseModulus = document.getElementById('base-modulus')?.value || '2000';
            wsData.push([currentLanguage === 'ar' ? 'طبقة الأساس' : 'Base Layer', baseModulus]);
            
            wsData.push(['']);
            
            // Add mix information
            const mixTypeEl = document.querySelector('input[name="mix-type"]:checked');
            let mixTypeText = '';
            if (mixTypeEl) {
                const mixType = mixTypeEl.value;
                if (mixType === 'surface') {
                    mixTypeText = currentLanguage === 'ar' ? 'سطحية' : 'Surface';
                } else if (mixType === 'binder') {
                    mixTypeText = currentLanguage === 'ar' ? 'رابطة' : 'Binder';
                } else {
                    mixTypeText = currentLanguage === 'ar' ? 'أساس' : 'Base';
                }
            }
            
            wsData.push([currentLanguage === 'ar' ? 'معلومات الخلطة' : 'Mix Information', '']);
            wsData.push(['']);
            wsData.push([currentLanguage === 'ar' ? 'نوع الخلطة' : 'Mix Type', mixTypeText]);
            
            const optimumAsphalt = document.getElementById('optimum-asphalt')?.textContent || '--';
            wsData.push([currentLanguage === 'ar' ? 'النسبة المثلى للزفت' : 'Optimum Asphalt Content', optimumAsphalt]);
            
            wsData.push(['']);
            
            // Add sieve data section
            wsData.push([currentLanguage === 'ar' ? 'التدرج المنخلي' : 'Sieve Gradation', '']);
            wsData.push(['']);
            wsData.push([
                currentLanguage === 'ar' ? 'المنخل' : 'Sieve',
                currentLanguage === 'ar' ? 'المار (%)' : 'Passing (%)',
                currentLanguage === 'ar' ? 'الحد الأدنى (%)' : 'Min (%)',
                currentLanguage === 'ar' ? 'الحد الأعلى (%)' : 'Max (%)'
            ]);
            
            if (mixTypeEl) {
                const mixType = mixTypeEl.value;
                const specTypeEl = document.getElementById('spec-type');
                if (specTypeEl) {
                    const specType = specTypeEl.value;
                    const limits = specLimits[mixType][specType];
                    
                    sieves.forEach((sieve, index) => {
                        const input = document.getElementById(sieve.id);
                        const value = input && input.value ? input.value : '-';
                        const sieveName = currentLanguage === 'ar' ? sieve.sizeAr : sieve.size;
                        
                        wsData.push([
                            `${sieveName} (${sieve.sizeMm} mm)`,
                            value,
                            limits[index].min,
                            limits[index].max
                        ]);
                    });
                }
            }
            
            // Create worksheet
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            
            // Set column widths for better display
            ws['!cols'] = [
                { wch: 30 }, // Column A - wider for Arabic text
                { wch: 15 }, // Column B
                { wch: 15 }, // Column C
                { wch: 15 }  // Column D
            ];
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, currentLanguage === 'ar' ? 'تقرير المشروع' : 'Project Report');
            
            // Write file with proper encoding
            try {
                XLSX.writeFile(wb, fileName, {
                    bookType: 'xlsx',
                    type: 'binary',
                    cellStyles: true,
                    compression: true
                });
            } catch (err) {
                console.error('Error generating Excel file:', err);
                alert(currentLanguage === 'ar' ? 'حدث خطأ أثناء إنشاء ملف Excel' : 'Error generating Excel file');
            }
        }
        
        // Save current project
        function saveCurrentProject() {
            // Get project data
            const projectNameEl = document.getElementById('project-name');
            if (!projectNameEl) {
                alert(currentLanguage === 'ar' ? 'لم يتم العثور على اسم المشروع' : 'Project name not found');
                return;
            }
            
            const projectName = projectNameEl.value;
            
            if (!projectName) {
                alert(currentLanguage === 'ar' ? 'يرجى إدخال اسم المشروع أولاً' : 'Please enter a project name first');
                return;
            }
            
            // Collect all form values
            const projectData = {
                id: Date.now(), // Unique id based on timestamp
                name: projectName,
                location: document.getElementById('project-location')?.value || '',
                soilType: document.getElementById('soil-type')?.value || 'sandy',
                cbrValue: document.getElementById('cbr-value')?.value || '6',
                serviceTemp: document.getElementById('service-temp')?.value || '40',
                trafficLevel: document.getElementById('traffic-level')?.value || 'high',
                esalValue: document.getElementById('esal-value')?.value || '2.5',
                trafficGrowth: document.getElementById('traffic-growth')?.value || '3',
                designLife: document.getElementById('design-life')?.value || '20',
                reliabilityFactor: document.getElementById('reliability-factor')?.value || '90',
                specType: document.getElementById('spec-type')?.value || 'aashto',
                mixType: document.querySelector('input[name="mix-type"]:checked')?.value || 'surface',
                surfaceModulus: document.getElementById('surface-modulus')?.value || '3000',
                binderModulus: document.getElementById('binder-modulus')?.value || '2500',
                baseModulus: document.getElementById('base-modulus')?.value || '2000',
                surfaceCoef: document.getElementById('surface-coef')?.value || '0.42',
                binderCoef: document.getElementById('binder-coef')?.value || '0.38',
                baseCoef: document.getElementById('base-coef')?.value || '0.14',
                binderDrainage: document.getElementById('binder-drainage')?.value || '1.0',
                baseDrainage: document.getElementById('base-drainage')?.value || '1.0',
                sieveValues: {}
            };
            
            // Collect sieve values
            sieves.forEach(sieve => {
                const input = document.getElementById(sieve.id);
                if (input && input.value) {
                    projectData.sieveValues[sieve.id] = input.value;
                }
            });
            
            // Check if project with same name exists
            const existingIndex = savedProjects.findIndex(project => project.name === projectName);
            
            if (existingIndex !== -1) {
                const confirmOverwrite = confirm(
                    currentLanguage === 'ar' 
                        ? `المشروع "${projectName}" موجود بالفعل. هل تريد استبداله؟` 
                        : `Project "${projectName}" already exists. Do you want to replace it?`
                );
                
                if (confirmOverwrite) {
                    savedProjects[existingIndex] = projectData;
                } else {
                    return;
                }
            } else {
                // Add to saved projects
                savedProjects.push(projectData);
            }
            
            // Save to localStorage
            try {
                localStorage.setItem('asphaltmixprojects', JSON.stringify(savedProjects));
            } catch (e) {
                console.error('Error saving to localStorage:', e);
                alert(currentLanguage === 'ar' ? 'حدث خطأ أثناء حفظ المشروع' : 'Error saving the project');
                return;
            }
            
            // Update saved projects display
            updateSavedProjectsList();
            
            // Show success message
            alert(
                currentLanguage === 'ar' 
                    ? `تم حفظ المشروع "${projectName}" بنجاح` 
                    : `Project "${projectName}" saved successfully`
            );
        }
        
        // Load a saved project
        function loadProject(projectId) {
            const project = savedProjects.find(p => p.id === projectId);
            
            if (!project) return;
            
            try {
                // Fill form values
                const projectNameEl = document.getElementById('project-name');
                if (projectNameEl) projectNameEl.value = project.name;
                
                const projectLocationEl = document.getElementById('project-location'); 
                if (projectLocationEl) projectLocationEl.value = project.location || '';
                
                const soilTypeEl = document.getElementById('soil-type');
                if (soilTypeEl) soilTypeEl.value = project.soilType || 'sandy';
                
                const cbrValueEl = document.getElementById('cbr-value');
                if (cbrValueEl) cbrValueEl.value = project.cbrValue || '6';
                
                const serviceTempEl = document.getElementById('service-temp');
                if (serviceTempEl) serviceTempEl.value = project.serviceTemp || '40';
                
                const trafficLevelEl = document.getElementById('traffic-level');
                if (trafficLevelEl) trafficLevelEl.value = project.trafficLevel || 'high';
                
                const esalValueEl = document.getElementById('esal-value');
                if (esalValueEl) esalValueEl.value = project.esalValue || '2.5';
                
                const trafficGrowthEl = document.getElementById('traffic-growth');
                if (trafficGrowthEl) trafficGrowthEl.value = project.trafficGrowth || '3';
                
                const designLifeEl = document.getElementById('design-life');
                if (designLifeEl) designLifeEl.value = project.designLife || '20';
                
                const reliabilityFactorEl = document.getElementById('reliability-factor');
                if (reliabilityFactorEl) reliabilityFactorEl.value = project.reliabilityFactor || '90';
                
                const specTypeEl = document.getElementById('spec-type');
                if (specTypeEl) specTypeEl.value = project.specType || 'aashto';
                
                const surfaceModulusEl = document.getElementById('surface-modulus');
                if (surfaceModulusEl) surfaceModulusEl.value = project.surfaceModulus || '3000';
                
                const binderModulusEl = document.getElementById('binder-modulus');
                if (binderModulusEl) binderModulusEl.value = project.binderModulus || '2500';
                
                const baseModulusEl = document.getElementById('base-modulus');
                if (baseModulusEl) baseModulusEl.value = project.baseModulus || '2000';
                
                const surfaceCoefEl = document.getElementById('surface-coef');
                if (surfaceCoefEl) surfaceCoefEl.value = project.surfaceCoef || '0.42';
                
                const binderCoefEl = document.getElementById('binder-coef');
                if (binderCoefEl) binderCoefEl.value = project.binderCoef || '0.38';
                
                const baseCoefEl = document.getElementById('base-coef');
                if (baseCoefEl) baseCoefEl.value = project.baseCoef || '0.14';
                
                const binderDrainageEl = document.getElementById('binder-drainage');
                if (binderDrainageEl) binderDrainageEl.value = project.binderDrainage || '1.0';
                
                const baseDrainageEl = document.getElementById('base-drainage');
                if (baseDrainageEl) baseDrainageEl.value = project.baseDrainage || '1.0';
                
                // Set mix type radio
                document.querySelectorAll('input[name="mix-type"]').forEach(radio => {
                    if (radio.value === project.mixType) {
                        radio.checked = true;
                    }
                });
                
                // Initialize sieve table before filling values
                initializeSieveTable();
                
                // Fill sieve values
                for (const [sieveId, value] of Object.entries(project.sieveValues)) {
                    const input = document.getElementById(sieveId);
                    if (input) {
                        input.value = value;
                    }
                }
                
                // Check conformity
                checkConformity();
                
                // Calculate thickness
                calculatePavementThickness();
                
                // Switch to project tab
                showTabContent('project');
                
                // Show success message
                alert(
                    currentLanguage === 'ar' 
                        ? `تم تحميل المشروع "${project.name}" بنجاح` 
                        : `Project "${project.name}" loaded successfully`
                );
            } catch (err) {
                console.error('Error loading project:', err);
                alert(
                    currentLanguage === 'ar' 
                        ? `حدث خطأ أثناء تحميل المشروع "${project.name}"` 
                        : `Error loading project "${project.name}"`
                );
            }
        }
        
        // Delete a saved project
        function deleteProject(projectId, event) {
            // Stop click from bubbling to load project
            if (event) {
                event.stopPropagation();
            }
            
            const project = savedProjects.find(p => p.id === projectId);
            
            if (!project) return;
            
            const confirmDelete = confirm(
                currentLanguage === 'ar' 
                    ? `هل تريد حذف المشروع "${project.name}"؟` 
                    : `Do you want to delete project "${project.name}"?`
            );
            
            if (confirmDelete) {
                savedProjects = savedProjects.filter(p => p.id !== projectId);
                // Save updated list to localStorage
                try {
                    localStorage.setItem('asphaltmixprojects', JSON.stringify(savedProjects));
                } catch (e) {
                    console.error('Error saving to localStorage:', e);
                }
                updateSavedProjectsList();
                
                // Show success message
                alert(
                    currentLanguage === 'ar' 
                        ? `تم حذف المشروع "${project.name}" بنجاح` 
                        : `Project "${project.name}" deleted successfully`
                );
            }
        }
        
        // Clear all saved projects
        function clearAllProjects() {
            const confirmClear = confirm(
                currentLanguage === 'ar' 
                    ? 'هل تريد حذف جميع المشاريع المحفوظة؟ هذا الإجراء لا يمكن التراجع عنه.' 
                    : 'Do you want to delete all saved projects? This action cannot be undone.'
            );
            
            if (confirmClear) {
                savedProjects = [];
                // Clear localStorage
                try {
                    localStorage.removeItem('asphaltmixprojects');
                } catch (e) {
                    console.error('Error clearing localStorage:', e);
                }
                updateSavedProjectsList();
                
                // Show success message
                alert(
                    currentLanguage === 'ar' 
                        ? 'تم حذف جميع المشاريع المحفوظة بنجاح' 
                        : 'All saved projects deleted successfully'
                );
            }
        }
        
        // Update saved projects list
        function updateSavedProjectsList() {
            const savedProjectsList = document.getElementById('saved-projects-list');
            const noSavedProjects = document.getElementById('no-saved-projects');
            const clearSavedProjectsBtn = document.getElementById('clear-saved-projects');
            
            if (!savedProjectsList || !noSavedProjects || !clearSavedProjectsBtn) return;
            
            if (savedProjects.length === 0) {
                noSavedProjects.classList.remove('hidden');
                clearSavedProjectsBtn.classList.add('hidden');
                clearSavedProjectsBtn.style.display = 'none';
                savedProjectsList.innerHTML = '';
                savedProjectsList.appendChild(noSavedProjects);
                return;
            }
            
            noSavedProjects.classList.add('hidden');
            clearSavedProjectsBtn.classList.remove('hidden');
            clearSavedProjectsBtn.style.display = 'flex';
            
            savedProjectsList.innerHTML = '';
            
            savedProjects.forEach(project => {
                const projectCard = document.createElement('div');
                projectCard.className = 'card p-4 rounded-lg mb-3 cursor-pointer hover:shadow-md transition-shadow';
                projectCard.onclick = () => loadProject(project.id);
                
                let mixTypeText = '';
                if (project.mixType === 'surface') {
                    mixTypeText = currentLanguage === 'ar' ? 'سطحية' : 'Surface';
                } else if (project.mixType === 'binder') {
                    mixTypeText = currentLanguage === 'ar' ? 'رابطة' : 'Binder';
                } else {
                    mixTypeText = currentLanguage === 'ar' ? 'أساس' : 'Base';
                }
                
                // Format date
                const projectDate = new Date(project.id);
                const dateStr = `${projectDate.toLocaleDateString()} ${projectDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                
                projectCard.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold text-lg">${project.name}</h3>
                            <p class="text-sm text-gray-500 mt-1">
                                ${project.location ? project.location + ' • ' : ''}${mixTypeText} • ${project.specType.toUpperCase()}
                            </p>
                            <span class="text-xs text-gray-400 block mt-1">
                                ${dateStr}
                            </span>
                        </div>
                        <button class="text-red-500 hover:text-red-700 p-2 bg-red-50 rounded-lg delete-project-btn" data-project-id="${project.id}">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                `;
                
                savedProjectsList.appendChild(projectCard);
            });
            
            // Add event listeners to delete buttons
            document.querySelectorAll('.delete-project-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const projectId = parseInt(btn.getAttribute('data-project-id'));
                    deleteProject(projectId, e);
                });
            });
        }

        // Switch language
        function switchLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            
            // Set language in HTML element
            document.documentElement.lang = currentLanguage;
            document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
            
            // Update language toggle text
            if (languageText) {
                languageText.textContent = currentLanguage === 'ar' ? 'English' : 'العربية';
            }
            
            // Update app title
            if (appTitle) {
                appTitle.textContent = currentLanguage === 'ar' ? 'تطبيق حساب سماكة طبقات الرصف وتحليل وتصميم الخلطات الاسفلتية' : 'Pavement Thickness Calculation and Asphalt Mix Design App';
            }
            
            // Update visible language elements
            document.querySelectorAll('.lang-ar').forEach(el => {
                el.style.display = currentLanguage === 'ar' ? 'inline-block' : 'none';
            });
            
            document.querySelectorAll('.lang-en').forEach(el => {
                el.style.display = currentLanguage === 'ar' ? 'none' : 'inline-block';
            });
            
            // Reinitialize sieve table
            initializeSieveTable();
            
            // Update charts if exists
            if (gradationChart) {
                updateGradationChart();
            }
            
            if (thicknessChart) {
                // Get current thickness values
                const surfaceThicknessEl = document.getElementById('surface-thickness');
                if (surfaceThicknessEl && surfaceThicknessEl.textContent && surfaceThicknessEl.textContent !== '--') {
                    const surfaceValue = parseFloat(surfaceThicknessEl.textContent);
                    const binderThicknessEl = document.getElementById('binder-thickness');
                    const baseThicknessEl = document.getElementById('base-thickness');
                    
                    if (binderThicknessEl && baseThicknessEl) {
                        const binderValue = parseFloat(binderThicknessEl.textContent);
                        const baseValue = parseFloat(baseThicknessEl.textContent);
                        updateThicknessChart(surfaceValue, binderValue, baseValue);
                    }
                }
            }
            
            // Update saved projects list
            updateSavedProjectsList();
            
            // Update active tab content
            const activeTab = document.querySelector('.tab-active');
            if (activeTab) {
                showTabContent(activeTab.id.replace('tab-', ''));
            }
            
            // Update thickness recommendations if they exist
            const thicknessRecommendationText = document.getElementById('thickness-recommendation-text');
            if (thicknessRecommendationText && thicknessRecommendationText.innerHTML) {
                calculatePavementThickness();
            }
            
            // No need to update tooltips since we're using CSS-only tooltips
        }

        // Switch to tab
        function showTabContent(tabName) {
            // Update tab buttons
            tabButtons.forEach(button => {
                if (button.id === `tab-${tabName}`) {
                    button.classList.add('tab-active');
                } else {
                    button.classList.remove('tab-active');
                }
            });
            
            // Update tab contents
            tabContents.forEach(content => {
                if (content.id === `content-${tabName}`) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });
            
            // Special actions for specific tabs
            if (tabName === 'analysis') {
                analyzeMix();
            } else if (tabName === 'report') {
                generateReport();
            } else if (tabName === 'saved') {
                updateSavedProjectsList();
            } else if (tabName === 'thickness') {
                // Calculate thickness when first showing the tab
                const surfaceThicknessEl = document.getElementById('surface-thickness');
                if (surfaceThicknessEl && (!surfaceThicknessEl.textContent || 
                    surfaceThicknessEl.textContent === '--')) {
                    calculatePavementThickness();
                }
            }
        }
        
        // Initialize the app
        function initializeApp() {
            console.log('Initializing app...');
            
            // Try to load saved projects from localStorage
            try {
                const savedProjectsData = localStorage.getItem('asphaltmixprojects');
                if (savedProjectsData) {
                    savedProjects = JSON.parse(savedProjectsData);
                    console.log('Loaded saved projects:', savedProjects.length);
                } else {
                    console.log('No saved projects found');
                }
            } catch (e) {
                console.error('Error loading saved projects:', e);
                savedProjects = [];
            }
            
            // Initialize tooltip system
            setupCustomTooltips();
            
            // Initialize tab navigation
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabName = button.id.replace('tab-', '');
                    showTabContent(tabName);
                });
            });
            
            // Initialize sieve table
            initializeSieveTable();
            
            // Language toggle
            const languageToggleBtn = document.getElementById('language-toggle');
            if (languageToggleBtn) {
                languageToggleBtn.addEventListener('click', switchLanguage);
            }
            
            // Quick fill buttons
            if (fillMinValuesBtn) fillMinValuesBtn.addEventListener('click', () => fillSieveValues('min'));
            if (fillMaxValuesBtn) fillMaxValuesBtn.addEventListener('click', () => fillSieveValues('max'));
            if (fillMidValuesBtn) fillMidValuesBtn.addEventListener('click', () => fillSieveValues('mid'));
            if (clearValuesBtn) clearValuesBtn.addEventListener('click', () => fillSieveValues('clear'));
            
            // Calculate thickness button
            if (calculateThicknessBtn) {
                calculateThicknessBtn.addEventListener('click', calculatePavementThickness);
            }
            
            // Mix type change
            document.querySelectorAll('input[name="mix-type"]').forEach(radio => {
                radio.addEventListener('change', initializeSieveTable);
            });
            
            // Specification type change
            const specTypeEl = document.getElementById('spec-type');
            if (specTypeEl) {
                specTypeEl.addEventListener('change', initializeSieveTable);
            }
            
            // Navigation buttons
            if (nextToGradationBtn) nextToGradationBtn.addEventListener('click', () => showTabContent('gradation'));
            if (backToProjectBtn) backToProjectBtn.addEventListener('click', () => showTabContent('project'));
            if (nextToThicknessBtn) nextToThicknessBtn.addEventListener('click', () => showTabContent('thickness'));
            if (backToGradationBtn) backToGradationBtn.addEventListener('click', () => showTabContent('gradation'));
            if (nextToAnalysisBtn) nextToAnalysisBtn.addEventListener('click', () => showTabContent('analysis'));
            if (backToThicknessBtn) backToThicknessBtn.addEventListener('click', () => showTabContent('thickness'));
            if (nextToReportBtn) nextToReportBtn.addEventListener('click', () => showTabContent('report'));
            if (backToAnalysisBtn) backToAnalysisBtn.addEventListener('click', () => showTabContent('analysis'));
            
            // PDF and Excel download
            const downloadPdfBtn = document.getElementById('download-pdf');
            const downloadExcelBtn = document.getElementById('download-excel');
            if (downloadPdfBtn) downloadPdfBtn.addEventListener('click', downloadPDF);
            if (downloadExcelBtn) downloadExcelBtn.addEventListener('click', downloadExcel);
            
            // Project management
            if (saveProjectBtn) saveProjectBtn.addEventListener('click', saveCurrentProject);
            if (clearSavedProjectsBtn) clearSavedProjectsBtn.addEventListener('click', clearAllProjects);
            
            // Initialize saved projects list
            updateSavedProjectsList();
            
            console.log('App initialization complete');
        }
        
        // Add window.deleteProject function for direct HTML access
        window.deleteProject = function(projectId, event) {
            deleteProject(projectId, event);
        };

        // Start the app when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
