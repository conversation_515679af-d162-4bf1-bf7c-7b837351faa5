/* Modern UI Components CSS - Based on Design Specification */

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap');

/* Modern Color Palette */
:root {
    /* Brand Colors */
    --primary-color: #4CAFEF;
    --primary-dark: #3A7BD5;
    --primary-light: #6BC5F2;
    --accent-color: #FFB300;
    --accent-dark: #FF8F00;
    --accent-light: #FFC947;
    
    /* Background Colors */
    --background-light: #FFFFFF;
    --background-dark: #121212;
    --background-secondary-light: #F8F9FA;
    --background-secondary-dark: #1E1E1E;
    
    /* Text Colors */
    --text-dark: #212121;
    --text-light: #FAFAFA;
    --text-muted: #6C757D;
    --text-secondary: #495057;
    
    /* Card & Shadow */
    --card-shadow: rgba(0, 0, 0, 0.1);
    --card-shadow-hover: rgba(0, 0, 0, 0.15);
    --card-background: #FFFFFF;
    --card-border: #E9ECEF;
    
    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #4CAFEF 0%, #3A7BD5 100%);
    --accent-gradient: linear-gradient(135deg, #FFB300 0%, #FF8F00 100%);
    
    /* Border Radius */
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-pill: 50px;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* Typography */
    --font-family-en: 'Poppins', 'Inter', sans-serif;
    --font-family-ar: 'Tajawal', 'Cairo', sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 30px;
    --line-height: 1.5;
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --background-light: #121212;
    --background-secondary-light: #1E1E1E;
    --card-background: #2D2D2D;
    --card-border: #404040;
    --text-dark: #FAFAFA;
    --text-secondary: #B0B0B0;
    --text-muted: #8A8A8A;
    --card-shadow: rgba(255, 255, 255, 0.05);
    --card-shadow-hover: rgba(255, 255, 255, 0.1);
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family-en);
    background-color: var(--background-light);
    color: var(--text-dark);
    line-height: var(--line-height);
    transition: all 0.3s ease;
}

/* Arabic Text */
[lang="ar"], .rtl {
    font-family: var(--font-family-ar);
    direction: rtl;
}

/* Modern Typography */
.text-h1 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    line-height: 1.2;
}

.text-h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    line-height: 1.3;
}

.text-body {
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: var(--line-height);
}

.text-small {
    font-size: var(--font-size-sm);
    font-weight: 400;
}

.text-muted {
    color: var(--text-muted);
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-height: 44px;
    gap: var(--spacing-sm);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 239, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Primary Button */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(76, 175, 239, 0.3);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(76, 175, 239, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Accent Button */
.btn-accent {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 179, 0, 0.3);
}

.btn-accent:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 179, 0, 0.4);
}

/* Button Sizes */
.btn-sm {
    padding: 8px 16px;
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: var(--font-size-lg);
    min-height: 52px;
}

/* Icon Button */
.btn-icon {
    width: 44px;
    height: 44px;
    padding: 0;
    border-radius: 50%;
}

/* Ripple Effect */
.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::before {
    width: 300px;
    height: 300px;
}

/* Modern Card Styles */
.card {
    background: var(--card-background);
    border: 1px solid var(--card-border);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 4px 20px var(--card-shadow);
    padding: var(--spacing-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px var(--card-shadow-hover);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-md);
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-md);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-dark);
}

.card-compact {
    padding: var(--spacing-md);
}

.card-elevated {
    box-shadow: 0 8px 32px var(--card-shadow);
}

/* Modern Navigation */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid var(--card-border);
    margin-bottom: var(--spacing-xl);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
    display: none;
}

.nav-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-muted);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;
}

.nav-tab:hover {
    color: var(--primary-color);
    background: rgba(76, 175, 239, 0.05);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 600;
}

.nav-tab.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px 2px 0 0;
}

/* Modern Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--card-border);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    background: var(--card-background);
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 239, 0.1);
}

.form-input:hover {
    border-color: var(--primary-light);
}

/* Input with Unit */
.input-with-unit {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.input-unit {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
    min-width: 30px;
}

/* Modern Header */
.header {
    background: var(--card-background);
    box-shadow: 0 2px 20px var(--card-shadow);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.header-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-logo {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-md);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* Badge */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: rgba(76, 175, 239, 0.1);
    color: var(--primary-color);
}

.badge-accent {
    background: rgba(255, 179, 0, 0.1);
    color: var(--accent-color);
}

.badge-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

/* Language Toggle */
.language-toggle {
    padding: 8px 16px;
    border: 2px solid var(--card-border);
    border-radius: var(--border-radius-pill);
    background: var(--card-background);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.language-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Modern Table */
.table-container {
    background: var(--card-background);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--card-shadow);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background: var(--background-secondary-light);
    padding: var(--spacing-md);
    text-align: right;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    border-bottom: 2px solid var(--card-border);
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--card-border);
}

.table tr:hover {
    background: rgba(76, 175, 239, 0.02);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in {
    animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading Animation */
.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(76, 175, 239, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .header-title {
        font-size: var(--font-size-lg);
    }
    
    .card {
        padding: var(--spacing-md);
    }
    
    .btn {
        padding: 10px 20px;
        font-size: var(--font-size-sm);
    }
    
    .nav-tabs {
        margin-bottom: var(--spacing-lg);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: 50%; }

.shadow-sm { box-shadow: 0 2px 8px var(--card-shadow); }
.shadow-md { box-shadow: 0 4px 20px var(--card-shadow); }
.shadow-lg { box-shadow: 0 8px 32px var(--card-shadow); }

/* Dark Mode Toggle */
.theme-toggle {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background: var(--card-background);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px var(--card-shadow);
}

.theme-toggle:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}