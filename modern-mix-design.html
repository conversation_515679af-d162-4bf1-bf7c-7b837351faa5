<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم الخلطات الإسفلتية - Modern UI</title>
    
    <!-- External Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- Modern UI Styles -->
    <link rel="stylesheet" href="modern-ui-components.css">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <style>
        /* Additional custom styles for the application */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }
        
        .main-content {
            min-height: calc(100vh - 80px);
            padding: var(--spacing-xl) 0;
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.6s ease;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Custom scrollbar for the entire page */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--background-secondary-light);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }
        
        /* Enhanced input styles */
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        
        /* Quick action buttons */
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }
        
        .quick-action-btn {
            padding: 8px 16px;
            border-radius: var(--border-radius-pill);
            font-size: var(--font-size-sm);
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-action-btn.fill {
            background: rgba(76, 175, 239, 0.1);
            color: var(--primary-color);
        }
        
        .quick-action-btn.fill:hover {
            background: rgba(76, 175, 239, 0.2);
        }
        
        .quick-action-btn.clear {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        
        .quick-action-btn.clear:hover {
            background: rgba(220, 53, 69, 0.2);
        }
        
        /* Navigation buttons */
        .nav-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-xl);
            gap: var(--spacing-md);
        }
        
        /* Results display */
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .result-card {
            background: var(--card-background);
            border: 1px solid var(--card-border);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: 0 4px 20px var(--card-shadow);
            transition: all 0.3s ease;
        }
        
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px var(--card-shadow-hover);
        }
        
        .result-value {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .result-label {
            font-size: var(--font-size-sm);
            color: var(--text-muted);
            font-weight: 500;
        }
        
        /* Chart container */
        .chart-container {
            background: var(--card-background);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            box-shadow: 0 4px 20px var(--card-shadow);
            margin-top: var(--spacing-lg);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .nav-buttons {
                flex-direction: column;
                gap: var(--spacing-md);
            }
            
            .nav-buttons > * {
                width: 100%;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen flex flex-col">
        <!-- Modern Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-brand">
                    <div class="header-logo">
                        <span class="material-icons-outlined">engineering</span>
                    </div>
                    <h1 class="header-title" id="app-title">تصميم الخلطات الإسفلتية</h1>
                </div>
                <div class="header-actions">
                    <!-- Version Badge -->
                    <span class="badge badge-primary">الإصدار 2.2</span>
                    
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="theme-toggle" title="تبديل الوضع المظلم">
                        <span class="material-icons-outlined">light_mode</span>
                    </button>
                    
                    <!-- Language Toggle -->
                    <button id="language-toggle" class="language-toggle">
                        <span id="language-text">English</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Modern Navigation Tabs -->
                <nav class="nav-tabs">
                    <button id="tab-project" class="nav-tab active">
                        <span class="material-icons-outlined" style="margin-left: 8px;">info</span>
                        <span class="lang-ar">معلومات المشروع</span>
                        <span class="lang-en hidden">Project Information</span>
                    </button>
                    <button id="tab-gradation" class="nav-tab">
                        <span class="material-icons-outlined" style="margin-left: 8px;">grain</span>
                        <span class="lang-ar">التدرج المنخلي</span>
                        <span class="lang-en hidden">Sieve Gradation</span>
                    </button>
                    <button id="tab-thickness" class="nav-tab">
                        <span class="material-icons-outlined" style="margin-left: 8px;">layers</span>
                        <span class="lang-ar">سماكة الطبقات</span>
                        <span class="lang-en hidden">Pavement Thickness</span>
                    </button>
                    <button id="tab-analysis" class="nav-tab">
                        <span class="material-icons-outlined" style="margin-left: 8px;">analytics</span>
                        <span class="lang-ar">تحليل الخلطة</span>
                        <span class="lang-en hidden">Mix Analysis</span>
                    </button>
                    <button id="tab-report" class="nav-tab">
                        <span class="material-icons-outlined" style="margin-left: 8px;">description</span>
                        <span class="lang-ar">التقرير</span>
                        <span class="lang-en hidden">Report</span>
                    </button>
                    <button id="tab-saved" class="nav-tab">
                        <span class="material-icons-outlined" style="margin-left: 8px;">folder</span>
                        <span class="lang-ar">المشاريع المحفوظة</span>
                        <span class="lang-en hidden">Saved Projects</span>
                    </button>
                </nav>

                <!-- Tab Content -->
                <div id="tab-contents">
                    <!-- Project Information Tab -->
                    <div id="content-project" class="tab-content active">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">info</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">معلومات المشروع</span>
                                    <span class="lang-en hidden">Project Information</span>
                                </h2>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">اسم المشروع</span>
                                        <span class="lang-en hidden">Project Name</span>
                                    </label>
                                    <input type="text" id="project-name" class="form-input" 
                                           placeholder="أدخل اسم المشروع">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">الموقع</span>
                                        <span class="lang-en hidden">Location</span>
                                    </label>
                                    <input type="text" id="project-location" class="form-input" 
                                           placeholder="أدخل موقع المشروع">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">المهندس المسؤول</span>
                                        <span class="lang-en hidden">Responsible Engineer</span>
                                    </label>
                                    <input type="text" id="engineer-name" class="form-input" 
                                           placeholder="أدخل اسم المهندس">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">التاريخ</span>
                                        <span class="lang-en hidden">Date</span>
                                    </label>
                                    <input type="date" id="project-date" class="form-input">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">نوع الطريق</span>
                                        <span class="lang-en hidden">Road Type</span>
                                    </label>
                                    <select id="road-type" class="form-input">
                                        <option value="">اختر نوع الطريق</option>
                                        <option value="highway">طريق سريع</option>
                                        <option value="arterial">طريق رئيسي</option>
                                        <option value="collector">طريق جامع</option>
                                        <option value="local">طريق محلي</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">حجم المرور المتوقع (ESAL)</span>
                                        <span class="lang-en hidden">Expected Traffic (ESAL)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="traffic-esal" class="form-input" 
                                               placeholder="0" min="0">
                                        <span class="input-unit">مليون</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="nav-buttons">
                                <button id="save-project" class="btn btn-accent">
                                    <span class="material-icons-outlined">save</span>
                                    <span class="lang-ar">حفظ المشروع</span>
                                    <span class="lang-en hidden">Save Project</span>
                                </button>
                                <button id="next-to-gradation" class="btn btn-primary">
                                    <span class="lang-ar">التالي</span>
                                    <span class="lang-en hidden">Next</span>
                                    <span class="material-icons-outlined">arrow_forward</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Sieve Gradation Tab -->
                    <div id="content-gradation" class="tab-content">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">grain</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">التدرج المنخلي</span>
                                    <span class="lang-en hidden">Sieve Gradation</span>
                                </h2>
                            </div>
                            
                            <!-- Quick Actions -->
                            <div class="quick-actions">
                                <button id="fill-min-values" class="quick-action-btn fill">
                                    <span class="lang-ar">ملء بالحدود الدنيا</span>
                                    <span class="lang-en hidden">Fill with Min Values</span>
                                </button>
                                <button id="fill-max-values" class="quick-action-btn fill">
                                    <span class="lang-ar">ملء بالحدود العليا</span>
                                    <span class="lang-en hidden">Fill with Max Values</span>
                                </button>
                                <button id="fill-mid-values" class="quick-action-btn fill">
                                    <span class="lang-ar">ملء بالقيم المتوسطة</span>
                                    <span class="lang-en hidden">Fill with Middle Values</span>
                                </button>
                                <button id="clear-values" class="quick-action-btn clear">
                                    <span class="lang-ar">مسح القيم</span>
                                    <span class="lang-en hidden">Clear Values</span>
                                </button>
                            </div>
                            
                            <!-- Gradation Table -->
                            <div class="table-container">
                                <table class="table" id="gradation-table">
                                    <thead>
                                        <tr>
                                            <th>
                                                <span class="lang-ar">حجم المنخل (مم)</span>
                                                <span class="lang-en hidden">Sieve Size (mm)</span>
                                            </th>
                                            <th>
                                                <span class="lang-ar">الحد الأدنى (%)</span>
                                                <span class="lang-en hidden">Min Limit (%)</span>
                                            </th>
                                            <th>
                                                <span class="lang-ar">الحد الأعلى (%)</span>
                                                <span class="lang-en hidden">Max Limit (%)</span>
                                            </th>
                                            <th>
                                                <span class="lang-ar">النسبة المختارة (%)</span>
                                                <span class="lang-en hidden">Selected Percentage (%)</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Table rows will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Chart Container -->
                            <div class="chart-container">
                                <canvas id="gradation-chart"></canvas>
                            </div>
                            
                            <div class="nav-buttons">
                                <button id="back-to-project" class="btn btn-secondary">
                                    <span class="material-icons-outlined">arrow_back</span>
                                    <span class="lang-ar">السابق</span>
                                    <span class="lang-en hidden">Previous</span>
                                </button>
                                <button id="next-to-thickness" class="btn btn-primary">
                                    <span class="lang-ar">التالي</span>
                                    <span class="lang-en hidden">Next</span>
                                    <span class="material-icons-outlined">arrow_forward</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Pavement Thickness Tab -->
                    <div id="content-thickness" class="tab-content">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">layers</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">حساب سماكة الطبقات</span>
                                    <span class="lang-en hidden">Pavement Thickness Calculation</span>
                                </h2>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">معامل المرونة للتربة (MR)</span>
                                        <span class="lang-en hidden">Subgrade Resilient Modulus (MR)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="subgrade-mr" class="form-input" 
                                               placeholder="0" min="0">
                                        <span class="input-unit">psi</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">معامل الموثوقية (R)</span>
                                        <span class="lang-en hidden">Reliability Factor (R)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="reliability" class="form-input" 
                                               placeholder="95" min="0" max="100" step="0.1">
                                        <span class="input-unit">%</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">الانحراف المعياري (So)</span>
                                        <span class="lang-en hidden">Standard Deviation (So)</span>
                                    </label>
                                    <input type="number" id="standard-deviation" class="form-input" 
                                           placeholder="0.45" min="0" step="0.01">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">مؤشر الخدمة الأولي (PSI)</span>
                                        <span class="lang-en hidden">Initial Serviceability Index (PSI)</span>
                                    </label>
                                    <input type="number" id="initial-psi" class="form-input" 
                                           placeholder="4.2" min="0" max="5" step="0.1">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">مؤشر الخدمة النهائي (PSI)</span>
                                        <span class="lang-en hidden">Terminal Serviceability Index (PSI)</span>
                                    </label>
                                    <input type="number" id="terminal-psi" class="form-input" 
                                           placeholder="2.5" min="0" max="5" step="0.1">
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button id="calculate-thickness" class="btn btn-primary btn-lg">
                                    <span class="material-icons-outlined">calculate</span>
                                    <span class="lang-ar">حساب السماكة</span>
                                    <span class="lang-en hidden">Calculate Thickness</span>
                                </button>
                            </div>
                            
                            <!-- Results Display -->
                            <div id="thickness-results" class="results-grid" style="display: none;">
                                <div class="result-card">
                                    <div class="result-value" id="sn-result">0</div>
                                    <div class="result-label">
                                        <span class="lang-ar">الرقم الهيكلي (SN)</span>
                                        <span class="lang-en hidden">Structural Number (SN)</span>
                                    </div>
                                </div>
                                <div class="result-card">
                                    <div class="result-value" id="thickness-result">0</div>
                                    <div class="result-label">
                                        <span class="lang-ar">السماكة المطلوبة (سم)</span>
                                        <span class="lang-en hidden">Required Thickness (cm)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="nav-buttons">
                                <button id="back-to-gradation" class="btn btn-secondary">
                                    <span class="material-icons-outlined">arrow_back</span>
                                    <span class="lang-ar">السابق</span>
                                    <span class="lang-en hidden">Previous</span>
                                </button>
                                <button id="next-to-analysis" class="btn btn-primary">
                                    <span class="lang-ar">التالي</span>
                                    <span class="lang-en hidden">Next</span>
                                    <span class="material-icons-outlined">arrow_forward</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Mix Analysis Tab -->
                    <div id="content-analysis" class="tab-content">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">analytics</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">تحليل الخلطة الإسفلتية</span>
                                    <span class="lang-en hidden">Asphalt Mix Analysis</span>
                                </h2>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">نسبة الإسفلت (%)</span>
                                        <span class="lang-en hidden">Asphalt Content (%)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="asphalt-content" class="form-input" 
                                               placeholder="5.5" min="0" max="10" step="0.1">
                                        <span class="input-unit">%</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">الكثافة النظرية القصوى (Gmm)</span>
                                        <span class="lang-en hidden">Theoretical Maximum Density (Gmm)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="gmm" class="form-input" 
                                               placeholder="2.500" min="0" step="0.001">
                                        <span class="input-unit">g/cm³</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">الكثافة الكلية (Gmb)</span>
                                        <span class="lang-en hidden">Bulk Density (Gmb)</span>
                                    </label>
                                    <div class="input-with-unit">
                                        <input type="number" id="gmb" class="form-input" 
                                               placeholder="2.400" min="0" step="0.001">
                                        <span class="input-unit">g/cm³</span>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">
                                        <span class="lang-ar">الثقل النوعي للإسفلت (Gb)</span>
                                        <span class="lang-en hidden">Specific Gravity of Asphalt (Gb)</span>
                                    </label>
                                    <input type="number" id="gb" class="form-input" 
                                           placeholder="1.020" min="0" step="0.001">
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button id="analyze-mix" class="btn btn-primary btn-lg">
                                    <span class="material-icons-outlined">science</span>
                                    <span class="lang-ar">تحليل الخلطة</span>
                                    <span class="lang-en hidden">Analyze Mix</span>
                                </button>
                            </div>
                            
                            <!-- Analysis Results -->
                            <div id="analysis-results" class="results-grid" style="display: none;">
                                <div class="result-card">
                                    <div class="result-value" id="vma-result">0</div>
                                    <div class="result-label">
                                        <span class="lang-ar">الفراغات في الركام المعدني (VMA %)</span>
                                        <span class="lang-en hidden">Voids in Mineral Aggregate (VMA %)</span>
                                    </div>
                                </div>
                                <div class="result-card">
                                    <div class="result-value" id="vfa-result">0</div>
                                    <div class="result-label">
                                        <span class="lang-ar">الفراغات المملوءة بالإسفلت (VFA %)</span>
                                        <span class="lang-en hidden">Voids Filled with Asphalt (VFA %)</span>
                                    </div>
                                </div>
                                <div class="result-card">
                                    <div class="result-value" id="va-result">0</div>
                                    <div class="result-label">
                                        <span class="lang-ar">الفراغات الهوائية (Va %)</span>
                                        <span class="lang-en hidden">Air Voids (Va %)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="nav-buttons">
                                <button id="back-to-thickness" class="btn btn-secondary">
                                    <span class="material-icons-outlined">arrow_back</span>
                                    <span class="lang-ar">السابق</span>
                                    <span class="lang-en hidden">Previous</span>
                                </button>
                                <button id="next-to-report" class="btn btn-primary">
                                    <span class="lang-ar">إنشاء التقرير</span>
                                    <span class="lang-en hidden">Generate Report</span>
                                    <span class="material-icons-outlined">arrow_forward</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Report Tab -->
                    <div id="content-report" class="tab-content">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">description</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">تقرير تصميم الخلطة الإسفلتية</span>
                                    <span class="lang-en hidden">Asphalt Mix Design Report</span>
                                </h2>
                            </div>
                            
                            <div id="report-content">
                                <!-- Report content will be generated by JavaScript -->
                                <div class="text-center p-8">
                                    <span class="material-icons-outlined" style="font-size: 48px; color: var(--text-muted);">description</span>
                                    <p class="text-muted mt-4">
                                        <span class="lang-ar">قم بإكمال جميع الخطوات لإنشاء التقرير</span>
                                        <span class="lang-en hidden">Complete all steps to generate the report</span>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="nav-buttons">
                                <button id="back-to-analysis" class="btn btn-secondary">
                                    <span class="material-icons-outlined">arrow_back</span>
                                    <span class="lang-ar">السابق</span>
                                    <span class="lang-en hidden">Previous</span>
                                </button>
                                <div class="flex gap-md">
                                    <button id="download-pdf" class="btn btn-accent">
                                        <span class="material-icons-outlined">picture_as_pdf</span>
                                        <span class="lang-ar">تحميل PDF</span>
                                        <span class="lang-en hidden">Download PDF</span>
                                    </button>
                                    <button id="download-excel" class="btn btn-accent">
                                        <span class="material-icons-outlined">table_chart</span>
                                        <span class="lang-ar">تحميل Excel</span>
                                        <span class="lang-en hidden">Download Excel</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Saved Projects Tab -->
                    <div id="content-saved" class="tab-content">
                        <div class="card card-elevated fade-in">
                            <div class="card-header">
                                <div class="card-icon">
                                    <span class="material-icons-outlined">folder</span>
                                </div>
                                <h2 class="card-title">
                                    <span class="lang-ar">المشاريع المحفوظة</span>
                                    <span class="lang-en hidden">Saved Projects</span>
                                </h2>
                            </div>
                            
                            <div id="saved-projects-list">
                                <!-- Saved projects will be populated by JavaScript -->
                                <div class="text-center p-8">
                                    <span class="material-icons-outlined" style="font-size: 48px; color: var(--text-muted);">folder_open</span>
                                    <p class="text-muted mt-4">
                                        <span class="lang-ar">لا توجد مشاريع محفوظة</span>
                                        <span class="lang-en hidden">No saved projects</span>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button id="clear-saved-projects" class="btn btn-secondary" style="display: none;">
                                    <span class="material-icons-outlined">delete</span>
                                    <span class="lang-ar">مسح جميع المشاريع</span>
                                    <span class="lang-en hidden">Clear All Projects</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // Modern UI JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the application
            initializeApp();
        });

        function initializeApp() {
            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('.material-icons-outlined');
            
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                themeIcon.textContent = newTheme === 'dark' ? 'dark_mode' : 'light_mode';
                
                // Save theme preference
                localStorage.setItem('theme', newTheme);
            });
            
            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            themeIcon.textContent = savedTheme === 'dark' ? 'dark_mode' : 'light_mode';
            
            // Tab navigation
            initializeTabNavigation();
            
            // Language toggle
            initializeLanguageToggle();
            
            // Form functionality
            initializeFormHandlers();
            
            // Initialize gradation table
            initializeGradationTable();
        }

        function initializeTabNavigation() {
            const tabButtons = document.querySelectorAll('.nav-tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.id.replace('tab-', '');
                    
                    // Update active tab
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Show corresponding content
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === `content-${tabId}`) {
                            content.classList.add('active');
                        }
                    });
                });
            });
        }

        function initializeLanguageToggle() {
            const languageToggle = document.getElementById('language-toggle');
            const languageText = document.getElementById('language-text');
            let isArabic = true;
            
            languageToggle.addEventListener('click', function() {
                isArabic = !isArabic;
                
                if (isArabic) {
                    document.documentElement.setAttribute('lang', 'ar');
                    document.documentElement.setAttribute('dir', 'rtl');
                    languageText.textContent = 'English';
                    document.querySelectorAll('.lang-ar').forEach(el => el.classList.remove('hidden'));
                    document.querySelectorAll('.lang-en').forEach(el => el.classList.add('hidden'));
                } else {
                    document.documentElement.setAttribute('lang', 'en');
                    document.documentElement.setAttribute('dir', 'ltr');
                    languageText.textContent = 'العربية';
                    document.querySelectorAll('.lang-ar').forEach(el => el.classList.add('hidden'));
                    document.querySelectorAll('.lang-en').forEach(el => el.classList.remove('hidden'));
                }
            });
        }

        function initializeFormHandlers() {
            // Navigation buttons
            document.getElementById('next-to-gradation')?.addEventListener('click', () => switchTab('gradation'));
            document.getElementById('back-to-project')?.addEventListener('click', () => switchTab('project'));
            document.getElementById('next-to-thickness')?.addEventListener('click', () => switchTab('thickness'));
            document.getElementById('back-to-gradation')?.addEventListener('click', () => switchTab('gradation'));
            document.getElementById('next-to-analysis')?.addEventListener('click', () => switchTab('analysis'));
            document.getElementById('back-to-thickness')?.addEventListener('click', () => switchTab('thickness'));
            document.getElementById('next-to-report')?.addEventListener('click', () => switchTab('report'));
            document.getElementById('back-to-analysis')?.addEventListener('click', () => switchTab('analysis'));
            
            // Calculation buttons
            document.getElementById('calculate-thickness')?.addEventListener('click', calculateThickness);
            document.getElementById('analyze-mix')?.addEventListener('click', analyzeMix);
            
            // Quick action buttons
            document.getElementById('fill-min-values')?.addEventListener('click', () => fillGradationValues('min'));
            document.getElementById('fill-max-values')?.addEventListener('click', () => fillGradationValues('max'));
            document.getElementById('fill-mid-values')?.addEventListener('click', () => fillGradationValues('mid'));
            document.getElementById('clear-values')?.addEventListener('click', clearGradationValues);
        }

        function switchTab(tabName) {
            document.getElementById(`tab-${tabName}`).click();
        }

        function initializeGradationTable() {
            const tableBody = document.querySelector('#gradation-table tbody');
            const sieveSizes = [25, 19, 12.5, 9.5, 4.75, 2.36, 1.18, 0.6, 0.3, 0.15, 0.075];
            const limits = {
                25: [100, 100],
                19: [95, 100],
                12.5: [80, 95],
                9.5: [70, 88],
                4.75: [50, 70],
                2.36: [35, 55],
                1.18: [25, 40],
                0.6: [18, 30],
                0.3: [12, 22],
                0.15: [8, 16],
                0.075: [4, 8]
            };
            
            tableBody.innerHTML = '';
            sieveSizes.forEach(size => {
                const row = document.createElement('tr');
                const [min, max] = limits[size];
                row.innerHTML = `
                    <td>${size}</td>
                    <td>${min}</td>
                    <td>${max}</td>
                    <td><input type="number" class="form-input" min="${min}" max="${max}" step="0.1" data-sieve="${size}"></td>
                `;
                tableBody.appendChild(row);
            });
        }

        function fillGradationValues(type) {
            const inputs = document.querySelectorAll('#gradation-table input[data-sieve]');
            const limits = {
                25: [100, 100],
                19: [95, 100],
                12.5: [80, 95],
                9.5: [70, 88],
                4.75: [50, 70],
                2.36: [35, 55],
                1.18: [25, 40],
                0.6: [18, 30],
                0.3: [12, 22],
                0.15: [8, 16],
                0.075: [4, 8]
            };
            
            inputs.forEach(input => {
                const sieve = parseFloat(input.dataset.sieve);
                const [min, max] = limits[sieve];
                
                switch(type) {
                    case 'min':
                        input.value = min;
                        break;
                    case 'max':
                        input.value = max;
                        break;
                    case 'mid':
                        input.value = ((min + max) / 2).toFixed(1);
                        break;
                }
            });
        }

        function clearGradationValues() {
            const inputs = document.querySelectorAll('#gradation-table input[data-sieve]');
            inputs.forEach(input => input.value = '');
        }

        function calculateThickness() {
            const esal = parseFloat(document.getElementById('traffic-esal')?.value) || 0;
            const mr = parseFloat(document.getElementById('subgrade-mr')?.value) || 0;
            const reliability = parseFloat(document.getElementById('reliability')?.value) || 95;
            const so = parseFloat(document.getElementById('standard-deviation')?.value) || 0.45;
            const initialPsi = parseFloat(document.getElementById('initial-psi')?.value) || 4.2;
            const terminalPsi = parseFloat(document.getElementById('terminal-psi')?.value) || 2.5;
            
            if (esal === 0 || mr === 0) {
                alert('يرجى إدخال جميع القيم المطلوبة');
                return;
            }
            
            // AASHTO 1993 equation (simplified)
            const zr = getZrValue(reliability);
            const deltaPsi = initialPsi - terminalPsi;
            const logW18 = Math.log10(esal * 1000000);
            const logMr = Math.log10(mr);
            
            // Structural Number calculation
            const sn = (logW18 - zr * so - 9.36 * Math.log10(deltaPsi + 1) - 0.20 + 
                       2.32 * logMr - 8.07) / 0.40;
            
            // Assuming layer coefficients: a1=0.44 (asphalt), a2=0.14 (base), a3=0.11 (subbase)
            const a1 = 0.44;
            const thickness = (sn / a1) * 2.54; // Convert to cm
            
            // Display results
            document.getElementById('sn-result').textContent = sn.toFixed(2);
            document.getElementById('thickness-result').textContent = thickness.toFixed(1);
            document.getElementById('thickness-results').style.display = 'grid';
        }

        function getZrValue(reliability) {
            const zrValues = {
                50: 0.000,
                60: -0.253,
                70: -0.524,
                75: -0.674,
                80: -0.841,
                85: -1.037,
                90: -1.282,
                95: -1.645,
                99: -2.326,
                99.9: -3.090
            };
            
            return zrValues[reliability] || -1.645;
        }

        function analyzeMix() {
            const asphaltContent = parseFloat(document.getElementById('asphalt-content')?.value) || 0;
            const gmm = parseFloat(document.getElementById('gmm')?.value) || 0;
            const gmb = parseFloat(document.getElementById('gmb')?.value) || 0;
            const gb = parseFloat(document.getElementById('gb')?.value) || 1.020;
            
            if (asphaltContent === 0 || gmm === 0 || gmb === 0) {
                alert('يرجى إدخال جميع القيم المطلوبة');
                return;
            }
            
            // Calculate mix properties
            const va = ((gmm - gmb) / gmm) * 100; // Air voids
            const vma = 100 - (gmb * (100 - asphaltContent) / 2.65); // VMA (assuming Gsb = 2.65)
            const vfa = ((vma - va) / vma) * 100; // VFA
            
            // Display results
            document.getElementById('va-result').textContent = va.toFixed(1);
            document.getElementById('vma-result').textContent = vma.toFixed(1);
            document.getElementById('vfa-result').textContent = vfa.toFixed(1);
            document.getElementById('analysis-results').style.display = 'grid';
        }
    </script>
</body>
</html>